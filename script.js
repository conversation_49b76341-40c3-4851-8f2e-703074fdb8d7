// Global variables
let uploadedFiles = JSON.parse(localStorage.getItem('uploadedFiles')) || {};
let currentFileId = null;

// DOM Elements
const dragArea = document.getElementById('dragArea');
const fileInput = document.getElementById('fileInput');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const resultSection = document.getElementById('resultSection');
const directLink = document.getElementById('directLink');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // Drag and drop events
    dragArea.addEventListener('dragover', handleDragOver);
    dragArea.addEventListener('dragleave', handleDragLeave);
    dragArea.addEventListener('drop', handleDrop);
    
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
    
    // Click on drag area
    dragArea.addEventListener('click', () => fileInput.click());
}

function handleDragOver(e) {
    e.preventDefault();
    dragArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    dragArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    dragArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    // Validate file type
    if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
        alert('يرجى اختيار ملف HTML فقط (.html أو .htm)');
        return;
    }
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
        return;
    }
    
    // Show file info
    showFileInfo(file);
    
    // Process file
    processFile(file);
}

function showFileInfo(file) {
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileInfo.classList.remove('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function processFile(file) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        const content = e.target.result;
        const fileId = generateFileId();
        
        // Store file in localStorage (for demo purposes)
        uploadedFiles[fileId] = {
            name: file.name,
            content: content,
            uploadDate: new Date().toISOString(),
            size: file.size
        };
        
        localStorage.setItem('uploadedFiles', JSON.stringify(uploadedFiles));
        
        // Generate link and show result
        currentFileId = fileId;
        showResult(fileId);
    };
    
    reader.readAsText(file);
}

function generateFileId() {
    return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function showResult(fileId) {
    const baseUrl = window.location.origin + window.location.pathname.replace('index.html', '');
    const fileUrl = baseUrl + 'view.html?id=' + fileId;
    
    directLink.value = fileUrl;
    
    // Generate QR code
    generateQRCode(fileUrl);
    
    // Show result section
    resultSection.classList.add('show');
    
    // Scroll to result
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

function generateQRCode(url) {
    const canvas = document.getElementById('qrcode');
    QRCode.toCanvas(canvas, url, {
        width: 200,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#FFFFFF'
        }
    }, function (error) {
        if (error) console.error(error);
    });
}

function copyLink() {
    directLink.select();
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('bg-green-500');
    
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.classList.remove('bg-green-500');
    }, 2000);
}

function openLink() {
    window.open(directLink.value, '_blank');
}

function downloadQR() {
    const canvas = document.getElementById('qrcode');
    const link = document.createElement('a');
    link.download = 'qr-code.png';
    link.href = canvas.toDataURL();
    link.click();
}

// Share functions
function shareWhatsApp() {
    const url = encodeURIComponent(directLink.value);
    const text = encodeURIComponent('شاهد هذا الملف: ');
    window.open(`https://wa.me/?text=${text}${url}`, '_blank');
}

function shareTwitter() {
    const url = encodeURIComponent(directLink.value);
    const text = encodeURIComponent('شاهد هذا الملف: ');
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
}

function shareLinkedIn() {
    const url = encodeURIComponent(directLink.value);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
}

function shareFacebook() {
    const url = encodeURIComponent(directLink.value);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
}

// Web Share API (if supported)
function shareNative() {
    if (navigator.share) {
        navigator.share({
            title: 'ملف HTML',
            text: 'شاهد هذا الملف',
            url: directLink.value
        }).catch(console.error);
    }
}
