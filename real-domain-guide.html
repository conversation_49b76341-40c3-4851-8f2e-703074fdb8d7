<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل إنشاء دومين حقيقي - خطوة بخطوة</title>
    <meta name="description" content="تعلم كيفية إنشاء دومين حقيقي لملف HTML خطوة بخطوة">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .step-card {
            transition: all 0.3s ease;
            border-left: 4px solid #e5e7eb;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-left-color: #3b82f6;
        }
        .step-card.completed {
            border-left-color: #10b981;
            background: #f0fdf4;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .platform-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .platform-card:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        .platform-card.selected {
            border: 2px solid #3b82f6;
            background: #dbeafe;
        }
        .demo-browser {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .browser-header {
            background: #f3f4f6;
            padding: 8px 16px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
        }
        .browser-dots {
            display: flex;
            gap: 6px;
            margin-left: 12px;
        }
        .browser-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        .dot-red { background: #ef4444; }
        .dot-yellow { background: #f59e0b; }
        .dot-green { background: #10b981; }
        .url-bar {
            flex: 1;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Hero Section -->
    <section class="hero-gradient text-white">
        <div class="container mx-auto px-4 py-16">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">
                    <i class="fas fa-graduation-cap mr-3"></i>
                    دليل إنشاء دومين حقيقي
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    تعلم كيفية إنشاء دومين حقيقي لملف HTML خطوة بخطوة
                </p>
                <div class="flex justify-center items-center space-x-8 space-x-reverse text-lg mb-8">
                    <span><i class="fas fa-check text-green-300 mr-2"></i>مجاني 100%</span>
                    <span><i class="fas fa-clock text-blue-300 mr-2"></i>5 دقائق</span>
                    <span><i class="fas fa-globe text-yellow-300 mr-2"></i>دومين حقيقي</span>
                </div>
                
                <div class="bg-white bg-opacity-10 rounded-lg p-6 max-w-2xl mx-auto">
                    <h3 class="text-xl font-bold mb-4">⚠️ تنبيه مهم</h3>
                    <p class="text-lg opacity-90">
                        لا يمكن إنشاء دومين حقيقي من موقع HTML بسيط. 
                        هذا الدليل سيعلمك الطريقة الصحيحة لإنشاء دومين حقيقي مجاناً.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Platform Selection -->
    <section class="container mx-auto px-4 py-12">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">اختر المنصة المناسبة</h2>
            <p class="text-gray-600 text-lg">كل منصة تقدم دومين مجاني مع مميزات مختلفة</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div class="platform-card bg-white rounded-lg shadow-lg p-6 text-center" onclick="selectPlatform('netlify')">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-rocket text-2xl text-blue-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">Netlify</h3>
                <p class="text-gray-600 mb-4">الأسهل والأسرع</p>
                <div class="text-sm text-gray-500">
                    <p>✅ سحب وإفلات</p>
                    <p>✅ HTTPS تلقائي</p>
                    <p>✅ CDN مجاني</p>
                </div>
                <div class="mt-4 text-blue-600 font-bold">
                    yoursite.netlify.app
                </div>
            </div>
            
            <div class="platform-card bg-white rounded-lg shadow-lg p-6 text-center" onclick="selectPlatform('vercel')">
                <div class="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-bolt text-2xl text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">Vercel</h3>
                <p class="text-gray-600 mb-4">سريع جداً</p>
                <div class="text-sm text-gray-500">
                    <p>✅ أداء عالي</p>
                    <p>✅ Git integration</p>
                    <p>✅ Analytics</p>
                </div>
                <div class="mt-4 text-black font-bold">
                    yoursite.vercel.app
                </div>
            </div>
            
            <div class="platform-card bg-white rounded-lg shadow-lg p-6 text-center" onclick="selectPlatform('github')">
                <div class="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-github text-2xl text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">GitHub Pages</h3>
                <p class="text-gray-600 mb-4">للمطورين</p>
                <div class="text-sm text-gray-500">
                    <p>✅ Git workflow</p>
                    <p>✅ Version control</p>
                    <p>✅ مجاني للعامة</p>
                </div>
                <div class="mt-4 text-gray-800 font-bold">
                    username.github.io
                </div>
            </div>
            
            <div class="platform-card bg-white rounded-lg shadow-lg p-6 text-center" onclick="selectPlatform('surge')">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-terminal text-2xl text-green-600"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">Surge.sh</h3>
                <p class="text-gray-600 mb-4">للمحترفين</p>
                <div class="text-sm text-gray-500">
                    <p>✅ Command line</p>
                    <p>✅ Custom domains</p>
                    <p>✅ Simple setup</p>
                </div>
                <div class="mt-4 text-green-600 font-bold">
                    yoursite.surge.sh
                </div>
            </div>
        </div>
    </section>

    <!-- Step-by-Step Guide -->
    <section class="container mx-auto px-4 py-12">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">الخطوات التفصيلية</h2>
                <p class="text-gray-600 text-lg">اتبع هذه الخطوات للحصول على دومين حقيقي</p>
            </div>

            <!-- Netlify Steps -->
            <div id="netlify-steps" class="steps-container">
                <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-6">
                    <div class="flex items-start">
                        <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4 mt-1">1</div>
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-800 mb-3">اذهب إلى Netlify Drop</h3>
                            <p class="text-gray-600 mb-4">افتح الرابط التالي في متصفح جديد:</p>
                            <div class="demo-browser mb-4">
                                <div class="browser-header">
                                    <div class="browser-dots">
                                        <div class="browser-dot dot-red"></div>
                                        <div class="browser-dot dot-yellow"></div>
                                        <div class="browser-dot dot-green"></div>
                                    </div>
                                    <div class="url-bar">https://app.netlify.com/drop</div>
                                </div>
                                <div class="p-6 text-center bg-gray-50">
                                    <i class="fas fa-cloud-upload-alt text-4xl text-blue-500 mb-3"></i>
                                    <p class="text-gray-600">Drag and drop your site output folder here</p>
                                </div>
                            </div>
                            <a href="https://app.netlify.com/drop" target="_blank" 
                               class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg inline-block">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                فتح Netlify Drop
                            </a>
                        </div>
                    </div>
                </div>

                <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-6">
                    <div class="flex items-start">
                        <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4 mt-1">2</div>
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-800 mb-3">اسحب ملف HTML</h3>
                            <p class="text-gray-600 mb-4">اسحب ملف HTML الخاص بك إلى المنطقة المخصصة</p>
                            <div class="bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg p-8 text-center">
                                <i class="fas fa-file-code text-4xl text-blue-500 mb-3"></i>
                                <p class="text-blue-700 font-medium">اسحب ملف HTML هنا</p>
                                <p class="text-blue-600 text-sm mt-2">أو اضغط لاختيار الملف</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-6">
                    <div class="flex items-start">
                        <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4 mt-1">3</div>
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-800 mb-3">احصل على الدومين</h3>
                            <p class="text-gray-600 mb-4">بعد رفع الملف، ستحصل على دومين حقيقي فوراً:</p>
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                    <div>
                                        <p class="font-bold text-green-800">تم النشر بنجاح!</p>
                                        <p class="text-green-600 font-mono text-sm">https://amazing-site-123456.netlify.app</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-6">
                    <div class="flex items-start">
                        <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4 mt-1">4</div>
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-800 mb-3">انسخ الدومين واستخدمه</h3>
                            <p class="text-gray-600 mb-4">انسخ الدومين الحقيقي واستخدمه في أي مكان:</p>
                            <div class="flex">
                                <input type="text" id="sampleDomain" value="https://amazing-site-123456.netlify.app" readonly
                                       class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 font-mono text-sm">
                                <button onclick="copySampleDomain()" 
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button onclick="openSampleDomain()" 
                                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                                    <i class="fas fa-external-link-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- QR Generator -->
    <section class="bg-gray-100 py-12">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-qrcode mr-3"></i>
                        مولد QR للدومين
                    </h2>
                    <p class="text-gray-600">الصق دومينك الحقيقي هنا لتوليد رمز QR</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الدومين الحقيقي:</label>
                        <input type="url" id="domainInput" placeholder="https://your-site.netlify.app" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg">
                    </div>
                    
                    <div class="text-center mb-6">
                        <button onclick="generateQR()" 
                                class="bg-purple-500 hover:bg-purple-600 text-white px-8 py-3 rounded-lg font-medium">
                            <i class="fas fa-qrcode mr-2"></i>
                            توليد رمز QR
                        </button>
                    </div>
                    
                    <div id="qrResult" class="text-center hidden">
                        <div class="inline-block p-4 bg-gray-50 border rounded-lg" id="qrContainer">
                            <!-- QR will be generated here -->
                        </div>
                        <div class="mt-4">
                            <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-download mr-2"></i>تحميل QR
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tips Section -->
    <section class="container mx-auto px-4 py-12">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">نصائح مهمة</h2>
                <p class="text-gray-600">لضمان نجاح إنشاء الدومين</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-check text-green-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-800 mb-2">ما يجب فعله</h3>
                            <ul class="text-gray-600 space-y-1">
                                <li>✅ استخدم ملفات HTML صحيحة</li>
                                <li>✅ تأكد من صغر حجم الملف</li>
                                <li>✅ اختبر الملف محلياً أولاً</li>
                                <li>✅ استخدم أسماء ملفات بالإنجليزية</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-times text-red-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-800 mb-2">ما يجب تجنبه</h3>
                            <ul class="text-gray-600 space-y-1">
                                <li>❌ لا ترفع ملفات معطوبة</li>
                                <li>❌ لا تستخدم أسماء بالعربية</li>
                                <li>❌ لا ترفع ملفات كبيرة جداً</li>
                                <li>❌ لا تتوقع دومين مخصص مجاناً</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    دليل إنشاء دومين حقيقي
                </h3>
                <p class="text-gray-400 mb-6">الطريقة الصحيحة والمضمونة لإنشاء دومين حقيقي مجاناً</p>
                <div class="flex justify-center space-x-6 space-x-reverse">
                    <a href="https://app.netlify.com/drop" target="_blank" class="text-blue-400 hover:text-blue-300">
                        <i class="fas fa-rocket mr-2"></i>Netlify
                    </a>
                    <a href="https://vercel.com" target="_blank" class="text-gray-400 hover:text-gray-300">
                        <i class="fas fa-bolt mr-2"></i>Vercel
                    </a>
                    <a href="https://pages.github.com" target="_blank" class="text-gray-400 hover:text-gray-300">
                        <i class="fab fa-github mr-2"></i>GitHub Pages
                    </a>
                </div>
                <div class="mt-8 pt-8 border-t border-gray-700 text-gray-400">
                    <p>&copy; 2024 دليل إنشاء دومين حقيقي. جميع الحقوق محفوظة.</p>
                    <p class="mt-2">
                        <i class="fas fa-heart text-red-500"></i>
                        صُنع بحب لمساعدة المطورين العرب
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let selectedPlatform = 'netlify';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Real Domain Guide loaded');
            selectPlatform('netlify'); // Default selection
        });

        function selectPlatform(platform) {
            selectedPlatform = platform;

            // Remove selected class from all cards
            document.querySelectorAll('.platform-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selected class to clicked card
            if (event && event.target) {
                event.target.closest('.platform-card').classList.add('selected');
            }

            console.log('Selected platform:', platform);
        }

        function copySampleDomain() {
            const domainInput = document.getElementById('sampleDomain');

            if (navigator.clipboard) {
                navigator.clipboard.writeText(domainInput.value).then(() => {
                    showCopySuccess();
                });
            } else {
                domainInput.select();
                document.execCommand('copy');
                showCopySuccess();
            }

            function showCopySuccess() {
                const button = event.target.closest('button');
                const original = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.remove('bg-blue-500');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    button.innerHTML = original;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-blue-500');
                }, 2000);
            }
        }

        function openSampleDomain() {
            alert('هذا مجرد مثال. استخدم الدومين الحقيقي الذي ستحصل عليه من Netlify');
        }

        function generateQR() {
            const domainInput = document.getElementById('domainInput');
            const domain = domainInput.value.trim();

            if (!domain) {
                alert('يرجى إدخال الدومين أولاً');
                return;
            }

            if (!domain.startsWith('http')) {
                alert('يرجى إدخال دومين صحيح يبدأ بـ https://');
                return;
            }

            const qrResult = document.getElementById('qrResult');
            const qrContainer = document.getElementById('qrContainer');

            // Show loading
            qrContainer.innerHTML = `
                <div class="text-gray-500 p-6">
                    <i class="fas fa-spinner fa-spin text-3xl mb-3"></i>
                    <p>جاري توليد رمز QR...</p>
                </div>
            `;
            qrResult.classList.remove('hidden');

            // Generate QR using API
            const img = document.createElement('img');
            img.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(domain)}`;
            img.alt = 'QR Code';
            img.className = 'w-48 h-48 border rounded-lg shadow-sm';

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
                console.log('QR Code generated successfully');
            };

            img.onerror = function() {
                qrContainer.innerHTML = `
                    <div class="text-center p-6">
                        <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-3"></i>
                        <p class="text-red-600 mb-4">فشل في توليد رمز QR</p>
                        <p class="text-sm text-gray-500">تأكد من صحة الدومين وإعادة المحاولة</p>
                    </div>
                `;
            };
        }

        function downloadQR() {
            const img = document.querySelector('#qrContainer img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'domain-qr-code.png';
                link.href = img.src;
                link.click();
            }
        }

        // Add input validation for domain
        document.getElementById('domainInput').addEventListener('input', function(e) {
            const value = e.target.value;
            const isValid = value.startsWith('http') && value.includes('.');

            if (value && !isValid) {
                e.target.style.borderColor = '#ef4444';
            } else {
                e.target.style.borderColor = '#d1d5db';
            }
        });

        console.log('Real Domain Guide script loaded successfully');
    </script>
</body>
</html>
