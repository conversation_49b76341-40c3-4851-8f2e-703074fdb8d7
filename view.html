<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الملف - رفع ملفات HTML</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .file-viewer {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .file-header {
            background: #f7fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem;
        }
        .file-content {
            height: 70vh;
            overflow: auto;
        }
        .error-message {
            background: #fed7d7;
            border: 1px solid #fc8181;
            color: #c53030;
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
        }
        .spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-500 hover:text-blue-600 mr-4">
                        <i class="fas fa-arrow-right text-xl"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-800">
                        <i class="fas fa-file-code text-blue-500 mr-2"></i>
                        عرض الملف
                    </h1>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button onclick="openInNewTab()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        فتح في تبويب جديد
                    </button>
                    <button onclick="downloadFile()" 
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-download mr-2"></i>
                        تحميل
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Loading -->
        <div id="loading" class="loading">
            <div class="text-center">
                <div class="spinner mx-auto mb-4"></div>
                <p class="text-gray-600">جاري تحميل الملف...</p>
            </div>
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="error-message rounded-lg p-6 mb-6 hidden">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-2xl mr-4"></i>
                <div>
                    <h3 class="font-bold text-lg mb-2">خطأ في تحميل الملف</h3>
                    <p id="errorText">الملف غير موجود أو تم حذفه.</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="index.html" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg inline-block">
                    <i class="fas fa-home mr-2"></i>
                    العودة للصفحة الرئيسية
                </a>
            </div>
        </div>

        <!-- File Info -->
        <div id="fileInfo" class="bg-white rounded-lg shadow-md p-6 mb-6 hidden">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <i class="fas fa-file-code text-blue-500 text-2xl mr-4"></i>
                    <div>
                        <h2 id="fileName" class="text-xl font-semibold text-gray-800"></h2>
                        <p class="text-gray-600">
                            <span id="fileSize"></span> • 
                            تم الرفع في <span id="uploadDate"></span>
                        </p>
                    </div>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        <i class="fas fa-check-circle mr-1"></i>
                        متاح
                    </span>
                </div>
            </div>
        </div>

        <!-- File Viewer -->
        <div id="fileViewer" class="file-viewer bg-white shadow-md hidden">
            <div class="file-header">
                <div class="flex items-center justify-between">
                    <h3 class="font-semibold text-gray-800">
                        <i class="fas fa-eye mr-2"></i>
                        معاينة الملف
                    </h3>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="toggleViewMode()" id="viewModeBtn"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-code mr-1"></i>
                            عرض الكود
                        </button>
                    </div>
                </div>
            </div>
            <div class="file-content">
                <!-- Rendered View -->
                <iframe id="renderedView" class="w-full h-full border-0"></iframe>
                
                <!-- Code View -->
                <pre id="codeView" class="hidden p-4 text-sm bg-gray-900 text-green-400 overflow-auto h-full"><code id="codeContent"></code></pre>
            </div>
        </div>

        <!-- Share Section -->
        <div id="shareSection" class="bg-white rounded-lg shadow-md p-6 mt-6 hidden">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-share-alt mr-2"></i>
                مشاركة هذا الملف
            </h3>
            <div class="flex items-center mb-4">
                <input type="text" id="shareLink" readonly 
                       class="flex-1 px-4 py-2 border border-gray-300 rounded-r-lg bg-gray-50 text-sm">
                <button onclick="copyShareLink()" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-l-lg">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
            <div class="flex space-x-4 space-x-reverse">
                <button onclick="shareWhatsApp()" 
                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                    <i class="fab fa-whatsapp mr-2"></i>
                    واتساب
                </button>
                <button onclick="shareTwitter()" 
                        class="bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg">
                    <i class="fab fa-twitter mr-2"></i>
                    تويتر
                </button>
                <button onclick="shareLinkedIn()" 
                        class="bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg">
                    <i class="fab fa-linkedin mr-2"></i>
                    لينكدإن
                </button>
            </div>
        </div>
    </main>

    <script src="view.js"></script>
</body>
</html>
