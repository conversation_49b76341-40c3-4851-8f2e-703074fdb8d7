// Global variables
let currentFile = null;
let isCodeView = false;

// DOM Elements
const loading = document.getElementById('loading');
const errorMessage = document.getElementById('errorMessage');
const errorText = document.getElementById('errorText');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const uploadDate = document.getElementById('uploadDate');
const fileViewer = document.getElementById('fileViewer');
const renderedView = document.getElementById('renderedView');
const codeView = document.getElementById('codeView');
const codeContent = document.getElementById('codeContent');
const shareSection = document.getElementById('shareSection');
const shareLink = document.getElementById('shareLink');
const viewModeBtn = document.getElementById('viewModeBtn');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    loadFile();
});

function loadFile() {
    // Get file ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const fileId = urlParams.get('id');
    
    if (!fileId) {
        showError('معرف الملف غير صحيح');
        return;
    }
    
    // Load from localStorage
    const uploadedFiles = JSON.parse(localStorage.getItem('uploadedFiles')) || {};
    const file = uploadedFiles[fileId];
    
    if (!file) {
        showError('الملف غير موجود أو تم حذفه');
        return;
    }
    
    currentFile = file;
    displayFile(file);
}

function showError(message) {
    loading.style.display = 'none';
    errorText.textContent = message;
    errorMessage.classList.remove('hidden');
}

function displayFile(file) {
    // Hide loading
    loading.style.display = 'none';
    
    // Show file info
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    uploadDate.textContent = formatDate(file.uploadDate);
    fileInfo.classList.remove('hidden');
    
    // Show file viewer
    displayRenderedView(file.content);
    fileViewer.classList.remove('hidden');
    
    // Setup share section
    shareLink.value = window.location.href;
    shareSection.classList.remove('hidden');
}

function displayRenderedView(content) {
    // Create blob URL for the HTML content
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    renderedView.src = url;
    renderedView.onload = function() {
        URL.revokeObjectURL(url);
    };
}

function displayCodeView(content) {
    codeContent.textContent = content;
}

function toggleViewMode() {
    if (isCodeView) {
        // Switch to rendered view
        renderedView.classList.remove('hidden');
        codeView.classList.add('hidden');
        viewModeBtn.innerHTML = '<i class="fas fa-code mr-1"></i> عرض الكود';
        isCodeView = false;
    } else {
        // Switch to code view
        renderedView.classList.add('hidden');
        codeView.classList.remove('hidden');
        displayCodeView(currentFile.content);
        viewModeBtn.innerHTML = '<i class="fas fa-eye mr-1"></i> عرض مرئي';
        isCodeView = true;
    }
}

function openInNewTab() {
    if (currentFile) {
        const blob = new Blob([currentFile.content], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const newWindow = window.open(url, '_blank');
        
        // Clean up the URL after a delay
        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 1000);
    }
}

function downloadFile() {
    if (currentFile) {
        const blob = new Blob([currentFile.content], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = currentFile.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }
}

function copyShareLink() {
    shareLink.select();
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('bg-green-500');
    
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.classList.remove('bg-green-500');
    }, 2000);
}

// Share functions
function shareWhatsApp() {
    const url = encodeURIComponent(shareLink.value);
    const text = encodeURIComponent('شاهد هذا الملف: ');
    window.open(`https://wa.me/?text=${text}${url}`, '_blank');
}

function shareTwitter() {
    const url = encodeURIComponent(shareLink.value);
    const text = encodeURIComponent('شاهد هذا الملف: ');
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
}

function shareLinkedIn() {
    const url = encodeURIComponent(shareLink.value);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    return date.toLocaleDateString('ar-SA', options);
}

// Handle iframe errors
window.addEventListener('message', function(event) {
    if (event.data.type === 'iframe-error') {
        console.error('Iframe error:', event.data.error);
    }
});
