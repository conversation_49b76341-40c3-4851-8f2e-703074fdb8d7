<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع ملفات HTML - مشاركة سهلة</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .result-section {
            display: none;
        }
        .result-section.show {
            display: block;
        }
        .copy-btn {
            transition: all 0.2s ease;
        }
        .copy-btn:hover {
            transform: translateY(-1px);
        }
        .share-btn {
            transition: all 0.2s ease;
        }
        .share-btn:hover {
            transform: translateY(-2px);
        }
        .file-viewer {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .file-header {
            background: #f7fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem;
        }
        .file-content {
            height: 70vh;
            overflow: auto;
        }
        .view-section {
            display: none;
        }
        .view-section.show {
            display: block;
        }
        .spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-upload text-blue-500 mr-3"></i>
                    رفع ملفات HTML
                </h1>
                <p class="text-gray-600">ارفع ملف HTML واحصل على رابط مباشر + رمز QR للمشاركة</p>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">اسحب ملف HTML هنا</h3>
                    <p class="text-gray-500 mb-4">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-folder-open mr-2"></i>
                        اختر ملف HTML
                    </button>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3"></i>
                            <span id="fileName" class="font-medium text-gray-700"></span>
                        </div>
                        <span id="fileSize" class="text-sm text-gray-500"></span>
                    </div>
                </div>
            </div>

            <!-- Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-lg shadow-md p-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    تم رفع الملف بنجاح!
                </h3>

                <!-- Direct Link -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الرابط المباشر:</label>
                    <div class="flex">
                        <input type="text" id="directLink" readonly 
                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 text-sm">
                        <button onclick="copyLink()" 
                                class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="openLink()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="mb-6 text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-4">رمز QR:</label>
                    <div class="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                        <canvas id="qrcode"></canvas>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" 
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-download mr-2"></i>
                            تحميل رمز QR
                        </button>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="text-center">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">مشاركة الرابط:</h4>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button onclick="shareWhatsApp()"
                                class="share-btn bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-whatsapp mr-2"></i>
                            واتساب
                        </button>
                        <button onclick="shareTwitter()"
                                class="share-btn bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-twitter mr-2"></i>
                            تويتر
                        </button>
                        <button onclick="shareLinkedIn()"
                                class="share-btn bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-linkedin mr-2"></i>
                            لينكدإن
                        </button>
                        <button onclick="shareFacebook()"
                                class="share-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-facebook mr-2"></i>
                            فيسبوك
                        </button>
                    </div>

                    <!-- View File Button -->
                    <div class="mt-6">
                        <button onclick="viewFile()"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-medium">
                            <i class="fas fa-eye mr-2"></i>
                            عرض الملف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Viewer Section -->
        <div id="viewSection" class="view-section max-w-6xl mx-auto mt-8">
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-semibold text-gray-800">
                        <i class="fas fa-file-code text-blue-500 mr-2"></i>
                        عرض الملف: <span id="viewFileName"></span>
                    </h3>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="toggleViewMode()" id="viewModeBtn"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-code mr-2"></i>
                            عرض الكود
                        </button>
                        <button onclick="openInNewTab()"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            فتح في تبويب جديد
                        </button>
                        <button onclick="downloadFile()"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-download mr-2"></i>
                            تحميل
                        </button>
                    </div>
                </div>
            </div>

            <!-- File Viewer -->
            <div class="file-viewer bg-white shadow-md">
                <div class="file-header">
                    <div class="flex items-center justify-between">
                        <h4 class="font-semibold text-gray-800">
                            <i class="fas fa-eye mr-2"></i>
                            معاينة الملف
                        </h4>
                        <button onclick="closeViewer()"
                                class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="file-content">
                    <!-- Rendered View -->
                    <iframe id="renderedView" class="w-full h-full border-0"></iframe>

                    <!-- Code View -->
                    <pre id="codeView" class="hidden p-4 text-sm bg-gray-900 text-green-400 overflow-auto h-full"><code id="codeContent"></code></pre>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 رفع ملفات HTML. جميع الحقوق محفوظة.</p>
                <div class="mt-4 space-x-4 space-x-reverse">
                    <a href="#" class="hover:text-blue-500">سياسة الخصوصية</a>
                    <a href="#" class="hover:text-blue-500">تواصل معنا</a>
                    <a href="#" class="hover:text-blue-500">GitHub</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let uploadedFiles = JSON.parse(localStorage.getItem('uploadedFiles')) || {};
        let currentFileId = null;
        let currentFile = null;
        let isCodeView = false;

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const resultSection = document.getElementById('resultSection');
        const directLink = document.getElementById('directLink');
        const viewSection = document.getElementById('viewSection');
        const viewFileName = document.getElementById('viewFileName');
        const renderedView = document.getElementById('renderedView');
        const codeView = document.getElementById('codeView');
        const codeContent = document.getElementById('codeContent');
        const viewModeBtn = document.getElementById('viewModeBtn');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
        });

        function setupEventListeners() {
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Click on drag area
            dragArea.addEventListener('click', () => fileInput.click());
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                alert('يرجى اختيار ملف HTML فقط (.html أو .htm)');
                return;
            }

            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
                return;
            }

            // Show file info
            showFileInfo(file);

            // Process file
            processFile(file);
        }

        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function processFile(file) {
            const reader = new FileReader();

            reader.onload = function(e) {
                const content = e.target.result;
                const fileId = generateFileId();

                // Store file in localStorage (for demo purposes)
                uploadedFiles[fileId] = {
                    name: file.name,
                    content: content,
                    uploadDate: new Date().toISOString(),
                    size: file.size
                };

                localStorage.setItem('uploadedFiles', JSON.stringify(uploadedFiles));

                // Generate link and show result
                currentFileId = fileId;
                currentFile = uploadedFiles[fileId];
                showResult(fileId);
            };

            reader.readAsText(file);
        }

        function generateFileId() {
            return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function showResult(fileId) {
            const baseUrl = window.location.origin + window.location.pathname;
            const fileUrl = baseUrl + '?view=' + fileId;

            directLink.value = fileUrl;

            // Generate QR code
            generateQRCode(fileUrl);

            // Show result section
            resultSection.classList.add('show');

            // Scroll to result
            resultSection.scrollIntoView({ behavior: 'smooth' });
        }

        function generateQRCode(url) {
            const canvas = document.getElementById('qrcode');
            QRCode.toCanvas(canvas, url, {
                width: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) console.error(error);
            });
        }

        function copyLink() {
            directLink.select();
            document.execCommand('copy');

            // Show feedback
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.add('bg-green-500');

            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('bg-green-500');
            }, 2000);
        }

        function openLink() {
            window.open(directLink.value, '_blank');
        }

        function downloadQR() {
            const canvas = document.getElementById('qrcode');
            const link = document.createElement('a');
            link.download = 'qr-code.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // View file functions
        function viewFile() {
            if (currentFile) {
                viewFileName.textContent = currentFile.name;
                displayRenderedView(currentFile.content);
                viewSection.classList.add('show');
                viewSection.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function closeViewer() {
            viewSection.classList.remove('show');
            isCodeView = false;
            viewModeBtn.innerHTML = '<i class="fas fa-code mr-2"></i> عرض الكود';
        }

        function displayRenderedView(content) {
            // Create blob URL for the HTML content
            const blob = new Blob([content], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            renderedView.src = url;
            renderedView.onload = function() {
                URL.revokeObjectURL(url);
            };

            // Show rendered view, hide code view
            renderedView.classList.remove('hidden');
            codeView.classList.add('hidden');
        }

        function displayCodeView(content) {
            codeContent.textContent = content;
            // Show code view, hide rendered view
            codeView.classList.remove('hidden');
            renderedView.classList.add('hidden');
        }

        function toggleViewMode() {
            if (isCodeView) {
                // Switch to rendered view
                displayRenderedView(currentFile.content);
                viewModeBtn.innerHTML = '<i class="fas fa-code mr-2"></i> عرض الكود';
                isCodeView = false;
            } else {
                // Switch to code view
                displayCodeView(currentFile.content);
                viewModeBtn.innerHTML = '<i class="fas fa-eye mr-2"></i> عرض مرئي';
                isCodeView = true;
            }
        }

        function openInNewTab() {
            if (currentFile) {
                const blob = new Blob([currentFile.content], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const newWindow = window.open(url, '_blank');

                // Clean up the URL after a delay
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 1000);
            }
        }

        function downloadFile() {
            if (currentFile) {
                const blob = new Blob([currentFile.content], { type: 'text/html' });
                const url = URL.createObjectURL(blob);

                const link = document.createElement('a');
                link.href = url;
                link.download = currentFile.name;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                URL.revokeObjectURL(url);
            }
        }

        // Share functions
        function shareWhatsApp() {
            const url = encodeURIComponent(directLink.value);
            const text = encodeURIComponent('شاهد هذا الملف: ');
            window.open(`https://wa.me/?text=${text}${url}`, '_blank');
        }

        function shareTwitter() {
            const url = encodeURIComponent(directLink.value);
            const text = encodeURIComponent('شاهد هذا الملف: ');
            window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
        }

        function shareLinkedIn() {
            const url = encodeURIComponent(directLink.value);
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
        }

        function shareFacebook() {
            const url = encodeURIComponent(directLink.value);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }

        // Check if viewing a file from URL
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const viewFileId = urlParams.get('view');

            if (viewFileId) {
                const file = uploadedFiles[viewFileId];
                if (file) {
                    currentFile = file;
                    currentFileId = viewFileId;

                    // Hide upload section and show file directly
                    document.querySelector('main > div').style.display = 'none';

                    // Show file viewer
                    viewFileName.textContent = file.name;
                    displayRenderedView(file.content);
                    viewSection.classList.add('show');

                    // Update page title
                    document.title = `عرض الملف: ${file.name}`;
                } else {
                    alert('الملف غير موجود أو تم حذفه');
                }
            }
        });
    </script>
</body>
</html>
