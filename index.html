<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع ملفات HTML - مشاركة سهلة</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .result-section {
            display: none;
        }
        .result-section.show {
            display: block;
        }
        .copy-btn {
            transition: all 0.2s ease;
        }
        .copy-btn:hover {
            transform: translateY(-1px);
        }
        .share-btn {
            transition: all 0.2s ease;
        }
        .share-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-upload text-blue-500 mr-3"></i>
                    رفع ملفات HTML
                </h1>
                <p class="text-gray-600">ارفع ملف HTML واحصل على رابط مباشر + رمز QR للمشاركة</p>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">اسحب ملف HTML هنا</h3>
                    <p class="text-gray-500 mb-4">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-folder-open mr-2"></i>
                        اختر ملف HTML
                    </button>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3"></i>
                            <span id="fileName" class="font-medium text-gray-700"></span>
                        </div>
                        <span id="fileSize" class="text-sm text-gray-500"></span>
                    </div>
                </div>
            </div>

            <!-- Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-lg shadow-md p-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    تم رفع الملف بنجاح!
                </h3>

                <!-- Direct Link -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الرابط المباشر:</label>
                    <div class="flex">
                        <input type="text" id="directLink" readonly 
                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 text-sm">
                        <button onclick="copyLink()" 
                                class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="openLink()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="mb-6 text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-4">رمز QR:</label>
                    <div class="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                        <canvas id="qrcode"></canvas>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" 
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-download mr-2"></i>
                            تحميل رمز QR
                        </button>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="text-center">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">مشاركة الرابط:</h4>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <button onclick="shareWhatsApp()" 
                                class="share-btn bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-whatsapp mr-2"></i>
                            واتساب
                        </button>
                        <button onclick="shareTwitter()" 
                                class="share-btn bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-twitter mr-2"></i>
                            تويتر
                        </button>
                        <button onclick="shareLinkedIn()" 
                                class="share-btn bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-linkedin mr-2"></i>
                            لينكدإن
                        </button>
                        <button onclick="shareFacebook()" 
                                class="share-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-facebook mr-2"></i>
                            فيسبوك
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 رفع ملفات HTML. جميع الحقوق محفوظة.</p>
                <div class="mt-4 space-x-4 space-x-reverse">
                    <a href="#" class="hover:text-blue-500">سياسة الخصوصية</a>
                    <a href="#" class="hover:text-blue-500">تواصل معنا</a>
                    <a href="#" class="hover:text-blue-500">GitHub</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
