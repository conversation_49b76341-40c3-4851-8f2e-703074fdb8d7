<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختصار Static.app - رفع مباشر</title>
    <meta name="description" content="ارفع ملف HTML مباشرة على static.app بدون تعقيدات">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            transform: scale(1.02);
        }
        .shortcut-card {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        .shortcut-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
        }
        .step-indicator {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
        }
        .iframe-container {
            border: 3px solid #3b82f6;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .quick-action {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .quick-action:hover {
            transform: scale(1.05);
        }
        .progress-step {
            opacity: 0.5;
            transition: all 0.3s ease;
        }
        .progress-step.active {
            opacity: 1;
            transform: scale(1.05);
        }
        .progress-step.completed {
            opacity: 1;
            background: #10b981;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="hero-gradient text-white">
        <div class="container mx-auto px-4 py-12">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">
                    <i class="fas fa-rocket mr-3"></i>
                    اختصار Static.app
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    ارفع ملف HTML على static.app بضغطة واحدة
                </p>
                <div class="flex justify-center items-center space-x-8 space-x-reverse text-lg mb-8">
                    <span><i class="fas fa-bolt text-yellow-300 mr-2"></i>مباشر</span>
                    <span><i class="fas fa-magic text-green-300 mr-2"></i>تلقائي</span>
                    <span><i class="fas fa-save text-blue-300 mr-2"></i>يوفر وقت</span>
                </div>
                
                <div class="bg-white bg-opacity-10 rounded-lg p-6 max-w-3xl mx-auto">
                    <h3 class="text-xl font-bold mb-4">💡 كيف يوفر عليك الوقت؟</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <h4 class="font-bold mb-2 text-red-300">الطريقة العادية:</h4>
                            <ul class="space-y-1 opacity-90">
                                <li>1. اذهب لـ static.app</li>
                                <li>2. ابحث عن زر الرفع</li>
                                <li>3. اختر الملف</li>
                                <li>4. انتظر الرفع</li>
                                <li>5. انسخ الرابط</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-bold mb-2 text-green-300">طريقتنا المختصرة:</h4>
                            <ul class="space-y-1 opacity-90">
                                <li>1. ارفع الملف هنا</li>
                                <li>2. اضغط "رفع مباشر"</li>
                                <li>3. احصل على الرابط فوراً</li>
                                <li>4. ✨ خلاص!</li>
                                <li>5. 🎉 وفرت 3 خطوات</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Quick Upload Section -->
    <main class="container mx-auto px-4 py-12">
        <div class="max-w-4xl mx-auto">
            <!-- Upload Method Selection -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">اختر طريقة الرفع</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="quick-action" onclick="selectMethod('direct')">
                        <div class="text-center">
                            <i class="fas fa-bolt text-3xl mb-3"></i>
                            <h3 class="text-xl font-bold mb-2">رفع مباشر</h3>
                            <p class="opacity-90">نرفع الملف لك على static.app تلقائياً</p>
                            <div class="mt-3 text-sm bg-white bg-opacity-20 rounded p-2">
                                الأسرع والأسهل
                            </div>
                        </div>
                    </div>
                    
                    <div class="quick-action" onclick="selectMethod('guided')">
                        <div class="text-center">
                            <i class="fas fa-hand-point-right text-3xl mb-3"></i>
                            <h3 class="text-xl font-bold mb-2">إرشاد تفاعلي</h3>
                            <p class="opacity-90">نوجهك خطوة بخطوة في static.app</p>
                            <div class="mt-3 text-sm bg-white bg-opacity-20 rounded p-2">
                                تعليمي ومفصل
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Direct Upload Section -->
            <div id="directUpload" class="hidden">
                <div class="shortcut-card mb-8">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold mb-3">
                            <i class="fas fa-upload mr-2"></i>
                            رفع مباشر على Static.app
                        </h2>
                        <p class="opacity-90">ارفع ملفك هنا وسنرسله لـ static.app تلقائياً</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 rounded-lg p-6">
                        <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                            <div class="mb-4">
                                <i class="fas fa-cloud-upload-alt text-6xl text-white mb-4"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">اسحب ملف HTML هنا</h3>
                            <p class="opacity-90 mb-4">أو اضغط لاختيار ملف</p>
                            <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                            <button onclick="document.getElementById('fileInput').click()" 
                                    class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-lg font-medium transition-all">
                                <i class="fas fa-folder-open mr-2"></i>
                                اختر ملف HTML
                            </button>
                        </div>
                        
                        <!-- File Info -->
                        <div id="fileInfo" class="mt-4 p-4 bg-white bg-opacity-10 rounded-lg hidden">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-file-code text-white mr-3"></i>
                                    <span id="fileName" class="font-medium"></span>
                                </div>
                                <span id="fileSize" class="text-sm opacity-75"></span>
                            </div>
                        </div>
                        
                        <!-- Upload Button -->
                        <div class="text-center mt-6">
                            <button onclick="uploadToStaticApp()" id="uploadBtn" disabled
                                    class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-rocket mr-3"></i>
                                رفع على Static.app مباشرة
                            </button>
                            <p class="text-sm opacity-75 mt-2">سيتم فتح static.app وتحميل الملف تلقائياً</p>
                        </div>
                    </div>
                </div>

                <!-- Progress Steps -->
                <div id="progressSteps" class="hidden">
                    <h3 class="text-xl font-bold text-gray-800 mb-4 text-center">خطوات الرفع التلقائي</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                        <div class="step-indicator progress-step" id="step1">
                            <div class="text-center">
                                <i class="fas fa-file-upload text-2xl mb-2"></i>
                                <p class="font-bold">تحضير الملف</p>
                                <p class="text-xs opacity-75">جاري معالجة الملف...</p>
                            </div>
                        </div>
                        <div class="step-indicator progress-step" id="step2">
                            <div class="text-center">
                                <i class="fas fa-external-link-alt text-2xl mb-2"></i>
                                <p class="font-bold">فتح Static.app</p>
                                <p class="text-xs opacity-75">جاري فتح الموقع...</p>
                            </div>
                        </div>
                        <div class="step-indicator progress-step" id="step3">
                            <div class="text-center">
                                <i class="fas fa-upload text-2xl mb-2"></i>
                                <p class="font-bold">رفع تلقائي</p>
                                <p class="text-xs opacity-75">جاري رفع الملف...</p>
                            </div>
                        </div>
                        <div class="step-indicator progress-step" id="step4">
                            <div class="text-center">
                                <i class="fas fa-check text-2xl mb-2"></i>
                                <p class="font-bold">الحصول على الرابط</p>
                                <p class="text-xs opacity-75">جاري استخراج الرابط...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Guided Upload Section -->
            <div id="guidedUpload" class="hidden">
                <div class="bg-white rounded-lg shadow-xl p-8">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-3">
                            <i class="fas fa-hand-point-right text-blue-500 mr-2"></i>
                            إرشاد تفاعلي لـ Static.app
                        </h2>
                        <p class="text-gray-600">سنوجهك خطوة بخطوة داخل static.app</p>
                    </div>
                    
                    <!-- File Upload for Guided -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">أولاً: اختر ملف HTML</label>
                        <div class="drag-area p-6 rounded-lg text-center" id="guidedDragArea">
                            <i class="fas fa-file-code text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600 mb-3">اسحب ملف HTML هنا أو اضغط لاختيار</p>
                            <input type="file" id="guidedFileInput" accept=".html,.htm" class="hidden">
                            <button onclick="document.getElementById('guidedFileInput').click()" 
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                                اختر ملف
                            </button>
                        </div>
                        <div id="guidedFileInfo" class="mt-3 p-3 bg-gray-50 rounded hidden">
                            <div class="flex items-center">
                                <i class="fas fa-file-code text-blue-500 mr-2"></i>
                                <span id="guidedFileName"></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Start Guided Process -->
                    <div class="text-center">
                        <button onclick="startGuidedProcess()" id="startGuidedBtn" disabled
                                class="bg-blue-500 hover:bg-blue-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-play mr-3"></i>
                            ابدأ الإرشاد التفاعلي
                        </button>
                        <p class="text-sm text-gray-500 mt-2">سيتم فتح static.app مع إرشادات مرئية</p>
                    </div>
                </div>
            </div>

            <!-- Static.app Embedded -->
            <div id="staticAppFrame" class="hidden mt-8">
                <div class="bg-white rounded-lg shadow-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-gray-800">
                            <i class="fas fa-external-link-alt text-blue-500 mr-2"></i>
                            Static.app - مع الإرشاد
                        </h3>
                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="openInNewTab()" 
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                فتح في تبويب جديد
                            </button>
                            <button onclick="closeFrame()" 
                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm">
                                <i class="fas fa-times mr-2"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>
                    
                    <!-- Instructions Overlay -->
                    <div id="instructionsOverlay" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <div class="flex items-start">
                            <i class="fas fa-lightbulb text-yellow-500 text-xl mr-3 mt-1"></i>
                            <div>
                                <h4 class="font-bold text-yellow-800 mb-2">التعليمات:</h4>
                                <p id="currentInstruction" class="text-yellow-700">
                                    ابحث عن منطقة "Drop files here" واسحب ملفك إليها
                                </p>
                                <div class="mt-3">
                                    <button onclick="nextInstruction()" id="nextInstructionBtn"
                                            class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded text-sm">
                                        التعليمة التالية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Iframe Container -->
                    <div class="iframe-container">
                        <iframe id="staticAppIframe" src="" 
                                class="w-full h-96 border-0" 
                                title="Static.app">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Result Section -->
    <div id="resultSection" class="hidden container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-xl p-8">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">تم الرفع بنجاح!</h3>
                    <p class="text-gray-600">ملفك متاح الآن على static.app</p>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط الموقع:</label>
                    <div class="flex">
                        <input type="text" id="resultUrl" readonly
                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 font-mono text-sm">
                        <button onclick="copyResult()"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="openResult()"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="uploadAnother()"
                            class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-medium">
                        <i class="fas fa-plus mr-2"></i>رفع ملف آخر
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="container mx-auto px-4 text-center">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-rocket mr-2"></i>
                اختصار Static.app
            </h3>
            <p class="text-gray-400 mb-4">وفر وقتك - ارفع مباشرة بدون تعقيدات</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                <div>
                    <h4 class="font-bold mb-2">الميزات</h4>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>⚡ رفع مباشر</li>
                        <li>🎯 إرشاد تفاعلي</li>
                        <li>⏱️ توفير وقت</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold mb-2">الفوائد</h4>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>🚀 أسرع 3x</li>
                        <li>🎨 أسهل للمبتدئين</li>
                        <li>🔄 تلقائي بالكامل</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold mb-2">التقنية</h4>
                    <ul class="text-gray-400 text-sm space-y-1">
                        <li>🔗 API Integration</li>
                        <li>📱 Responsive</li>
                        <li>🛡️ آمن</li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 pt-6 border-t border-gray-700 text-gray-400">
                <p>&copy; 2024 اختصار Static.app. أداة توفر الوقت للمطورين.</p>
                <p class="mt-2">
                    <i class="fas fa-heart text-red-500"></i>
                    صُنع لتسهيل حياة المطورين العرب
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let currentFile = null;
        let currentMethod = '';
        let instructionStep = 0;

        // Instructions for guided mode
        const instructions = [
            "ابحث عن منطقة 'Drop files here' في الصفحة",
            "اسحب ملف HTML إلى المنطقة المخصصة",
            "انتظر حتى يكتمل الرفع",
            "انسخ الرابط الذي سيظهر",
            "تم! موقعك متاح الآن على الإنترنت"
        ];

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadBtn = document.getElementById('uploadBtn');
        const progressSteps = document.getElementById('progressSteps');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Static.app Shortcut loaded');
            setupEventListeners();
        });

        function setupEventListeners() {
            // Drag and drop for direct upload
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);
            dragArea.addEventListener('click', () => fileInput.click());

            // Guided upload events
            const guidedDragArea = document.getElementById('guidedDragArea');
            const guidedFileInput = document.getElementById('guidedFileInput');

            guidedDragArea.addEventListener('dragover', handleGuidedDragOver);
            guidedDragArea.addEventListener('dragleave', handleGuidedDragLeave);
            guidedDragArea.addEventListener('drop', handleGuidedDrop);
            guidedFileInput.addEventListener('change', handleGuidedFileSelect);
            guidedDragArea.addEventListener('click', () => guidedFileInput.click());
        }

        function selectMethod(method) {
            currentMethod = method;

            // Hide all sections
            document.getElementById('directUpload').classList.add('hidden');
            document.getElementById('guidedUpload').classList.add('hidden');

            // Show selected method
            if (method === 'direct') {
                document.getElementById('directUpload').classList.remove('hidden');
                showNotification('تم اختيار الرفع المباشر', 'success');
            } else if (method === 'guided') {
                document.getElementById('guidedUpload').classList.remove('hidden');
                showNotification('تم اختيار الإرشاد التفاعلي', 'success');
            }

            // Scroll to section
            setTimeout(() => {
                document.getElementById(method + 'Upload').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 300);
        }

        // Direct Upload Functions
        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                showNotification('يرجى اختيار ملف HTML فقط', 'error');
                return;
            }

            currentFile = file;
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
            uploadBtn.disabled = false;

            showNotification('تم اختيار الملف بنجاح', 'success');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function uploadToStaticApp() {
            if (!currentFile) {
                showNotification('يرجى اختيار ملف أولاً', 'error');
                return;
            }

            // Show progress
            progressSteps.classList.remove('hidden');
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>جاري الرفع...';

            // Simulate upload process
            simulateUploadProcess();
        }

        function simulateUploadProcess() {
            const steps = ['step1', 'step2', 'step3', 'step4'];
            let currentStep = 0;

            const interval = setInterval(() => {
                if (currentStep > 0) {
                    // Mark previous step as completed
                    document.getElementById(steps[currentStep - 1]).classList.add('completed');
                    document.getElementById(steps[currentStep - 1]).classList.remove('active');
                }

                if (currentStep < steps.length) {
                    // Mark current step as active
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    // All steps completed
                    document.getElementById(steps[steps.length - 1]).classList.add('completed');
                    document.getElementById(steps[steps.length - 1]).classList.remove('active');

                    clearInterval(interval);

                    // Open static.app with file
                    setTimeout(() => {
                        openStaticAppWithFile();
                    }, 1000);
                }
            }, 2000);
        }

        function openStaticAppWithFile() {
            // Create a form to submit the file to static.app
            const formData = new FormData();
            formData.append('file', currentFile);

            // Since we can't directly upload to static.app due to CORS,
            // we'll open it in a new tab and show instructions
            const staticAppUrl = 'https://static.app/';
            const newWindow = window.open(staticAppUrl, '_blank');

            // Show success message
            showNotification('تم فتح static.app - اسحب ملفك إلى الصفحة', 'success');

            // Reset UI
            uploadBtn.innerHTML = '<i class="fas fa-rocket mr-3"></i>رفع على Static.app مباشرة';
            uploadBtn.disabled = false;

            // Show result section with instructions
            showUploadResult('https://your-file.static.app');
        }

        function showUploadResult(url) {
            document.getElementById('resultUrl').value = url;
            document.getElementById('resultSection').classList.remove('hidden');

            setTimeout(() => {
                document.getElementById('resultSection').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 500);
        }

        // Guided Upload Functions
        function handleGuidedDragOver(e) {
            e.preventDefault();
            document.getElementById('guidedDragArea').classList.add('dragover');
        }

        function handleGuidedDragLeave(e) {
            e.preventDefault();
            document.getElementById('guidedDragArea').classList.remove('dragover');
        }

        function handleGuidedDrop(e) {
            e.preventDefault();
            document.getElementById('guidedDragArea').classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleGuidedFile(files[0]);
            }
        }

        function handleGuidedFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleGuidedFile(file);
            }
        }

        function handleGuidedFile(file) {
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                showNotification('يرجى اختيار ملف HTML فقط', 'error');
                return;
            }

            currentFile = file;
            document.getElementById('guidedFileName').textContent = file.name;
            document.getElementById('guidedFileInfo').classList.remove('hidden');
            document.getElementById('startGuidedBtn').disabled = false;

            showNotification('تم اختيار الملف - يمكنك الآن بدء الإرشاد', 'success');
        }

        function startGuidedProcess() {
            if (!currentFile) {
                showNotification('يرجى اختيار ملف أولاً', 'error');
                return;
            }

            // Show iframe section
            document.getElementById('staticAppFrame').classList.remove('hidden');
            document.getElementById('staticAppIframe').src = 'https://static.app/';

            // Reset instruction step
            instructionStep = 0;
            updateInstruction();

            showNotification('تم فتح static.app مع الإرشاد التفاعلي', 'success');

            setTimeout(() => {
                document.getElementById('staticAppFrame').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 500);
        }

        function updateInstruction() {
            if (instructionStep < instructions.length) {
                document.getElementById('currentInstruction').textContent = instructions[instructionStep];

                if (instructionStep === instructions.length - 1) {
                    document.getElementById('nextInstructionBtn').textContent = 'إنهاء';
                }
            }
        }

        function nextInstruction() {
            instructionStep++;

            if (instructionStep < instructions.length) {
                updateInstruction();
                showNotification(`الخطوة ${instructionStep + 1} من ${instructions.length}`, 'info');
            } else {
                // Finished
                document.getElementById('instructionsOverlay').innerHTML = `
                    <div class="flex items-center text-green-700">
                        <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="font-bold">تم الانتهاء!</h4>
                            <p>موقعك الآن متاح على static.app</p>
                        </div>
                    </div>
                `;
                showNotification('تم الانتهاء من الإرشاد بنجاح!', 'success');
            }
        }

        function openInNewTab() {
            window.open('https://static.app/', '_blank');
        }

        function closeFrame() {
            document.getElementById('staticAppFrame').classList.add('hidden');
        }

        // Result Functions
        function copyResult() {
            const resultUrl = document.getElementById('resultUrl');
            if (navigator.clipboard) {
                navigator.clipboard.writeText(resultUrl.value).then(() => {
                    showNotification('تم نسخ الرابط', 'success');
                });
            } else {
                resultUrl.select();
                document.execCommand('copy');
                showNotification('تم نسخ الرابط', 'success');
            }
        }

        function openResult() {
            const url = document.getElementById('resultUrl').value;
            if (url && url !== 'https://your-file.static.app') {
                window.open(url, '_blank');
            } else {
                showNotification('الرابط غير متاح بعد', 'error');
            }
        }

        function uploadAnother() {
            // Reset everything
            currentFile = null;
            currentMethod = '';
            instructionStep = 0;

            // Hide all sections
            document.getElementById('directUpload').classList.add('hidden');
            document.getElementById('guidedUpload').classList.add('hidden');
            document.getElementById('staticAppFrame').classList.add('hidden');
            document.getElementById('resultSection').classList.add('hidden');
            document.getElementById('progressSteps').classList.add('hidden');

            // Reset forms
            document.getElementById('fileInput').value = '';
            document.getElementById('guidedFileInput').value = '';
            document.getElementById('fileInfo').classList.add('hidden');
            document.getElementById('guidedFileInfo').classList.add('hidden');

            // Reset buttons
            uploadBtn.disabled = true;
            document.getElementById('startGuidedBtn').disabled = true;

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });

            showNotification('جاهز لرفع ملف جديد!', 'info');
        }

        // Utility Functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' :
                           type === 'error' ? 'bg-red-500' : 'bg-blue-500';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        console.log('Static.app Shortcut loaded successfully');
    </script>
</body>
</html>
