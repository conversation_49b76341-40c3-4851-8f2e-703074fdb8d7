<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة إنشاء الدومين - للمطورين</title>
    <meta name="description" content="أداة شاملة لإنشاء دومين وQR بسهولة">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .tool-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border-color: #3b82f6;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .quick-action {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .quick-action:hover {
            transform: scale(1.02);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .platform-quick {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .platform-quick:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        .status-online { background: #10b981; }
        .status-slow { background: #f59e0b; }
        .status-offline { background: #ef4444; }
        .domain-history {
            max-height: 300px;
            overflow-y: auto;
        }
        .domain-item {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            transition: all 0.2s ease;
        }
        .domain-item:hover {
            background: #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="hero-gradient text-white">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center">
                <h1 class="text-3xl md:text-4xl font-bold mb-4">
                    <i class="fas fa-tools mr-3"></i>
                    أداة إنشاء الدومين
                </h1>
                <p class="text-lg md:text-xl mb-6 opacity-90">
                    كل ما تحتاجه لإنشاء دومين وQR في مكان واحد
                </p>
                <div class="flex justify-center items-center space-x-6 space-x-reverse text-sm">
                    <span><i class="fas fa-rocket text-yellow-300 mr-1"></i>سريع</span>
                    <span><i class="fas fa-cog text-blue-300 mr-1"></i>شامل</span>
                    <span><i class="fas fa-save text-green-300 mr-1"></i>يحفظ التاريخ</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="container mx-auto px-4 py-8">
        <!-- Quick Actions -->
        <div class="max-w-6xl mx-auto mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">إجراءات سريعة</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="quick-action" onclick="openNetlifyDrop()">
                    <div class="text-center">
                        <i class="fas fa-rocket text-3xl mb-3"></i>
                        <h3 class="text-xl font-bold mb-2">Netlify Drop</h3>
                        <p class="opacity-90">الأسرع - اسحب وأفلت</p>
                    </div>
                </div>
                
                <div class="quick-action" onclick="openVercel()">
                    <div class="text-center">
                        <i class="fas fa-bolt text-3xl mb-3"></i>
                        <h3 class="text-xl font-bold mb-2">Vercel Deploy</h3>
                        <p class="opacity-90">أداء عالي</p>
                    </div>
                </div>
                
                <div class="quick-action" onclick="openSurge()">
                    <div class="text-center">
                        <i class="fas fa-terminal text-3xl mb-3"></i>
                        <h3 class="text-xl font-bold mb-2">Surge.sh</h3>
                        <p class="opacity-90">للمحترفين</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Platform Status -->
            <div class="tool-card bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-server mr-2"></i>
                    حالة المنصات
                </h3>
                
                <div class="space-y-3">
                    <div class="platform-quick" onclick="openNetlifyDrop()">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-rocket text-blue-500 mr-3"></i>
                                <div>
                                    <p class="font-semibold">Netlify Drop</p>
                                    <p class="text-sm text-gray-600">app.netlify.com/drop</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="status-indicator status-online"></span>
                                <span class="text-sm text-green-600">متاح</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="platform-quick" onclick="openVercel()">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-bolt text-black mr-3"></i>
                                <div>
                                    <p class="font-semibold">Vercel</p>
                                    <p class="text-sm text-gray-600">vercel.com</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="status-indicator status-online"></span>
                                <span class="text-sm text-green-600">متاح</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="platform-quick" onclick="openGitHub()">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fab fa-github text-gray-800 mr-3"></i>
                                <div>
                                    <p class="font-semibold">GitHub Pages</p>
                                    <p class="text-sm text-gray-600">pages.github.com</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="status-indicator status-slow"></span>
                                <span class="text-sm text-yellow-600">بطيء</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="platform-quick" onclick="openSurge()">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-terminal text-green-500 mr-3"></i>
                                <div>
                                    <p class="font-semibold">Surge.sh</p>
                                    <p class="text-sm text-gray-600">surge.sh</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="status-indicator status-online"></span>
                                <span class="text-sm text-green-600">متاح</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <button onclick="checkAllStatus()" 
                            class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-lg">
                        <i class="fas fa-sync-alt mr-2"></i>
                        تحديث الحالة
                    </button>
                </div>
            </div>

            <!-- QR Generator -->
            <div class="tool-card bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-qrcode mr-2"></i>
                    مولد QR سريع
                </h3>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الدومين:</label>
                    <input type="url" id="qrDomainInput" placeholder="https://your-site.netlify.app" 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg">
                </div>
                
                <div class="grid grid-cols-2 gap-3 mb-4">
                    <button onclick="generateQR('small')" 
                            class="bg-purple-500 hover:bg-purple-600 text-white py-2 rounded-lg">
                        <i class="fas fa-qrcode mr-2"></i>صغير
                    </button>
                    <button onclick="generateQR('large')" 
                            class="bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg">
                        <i class="fas fa-qrcode mr-2"></i>كبير
                    </button>
                </div>
                
                <div id="qrResult" class="text-center hidden">
                    <div class="inline-block p-4 bg-gray-50 border rounded-lg" id="qrContainer">
                        <!-- QR will be generated here -->
                    </div>
                    <div class="mt-3 space-x-2 space-x-reverse">
                        <button onclick="downloadQR()" id="downloadQRBtn" style="display:none;"
                                class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm">
                            <i class="fas fa-download mr-1"></i>تحميل
                        </button>
                        <button onclick="copyQRLink()" id="copyQRBtn" style="display:none;"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm">
                            <i class="fas fa-link mr-1"></i>نسخ رابط
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Domain History -->
        <div class="max-w-6xl mx-auto mt-8">
            <div class="tool-card bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-800">
                        <i class="fas fa-history mr-2"></i>
                        تاريخ الدومينات
                    </h3>
                    <div class="space-x-2 space-x-reverse">
                        <button onclick="addDomain()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-plus mr-2"></i>إضافة دومين
                        </button>
                        <button onclick="clearHistory()" 
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-trash mr-2"></i>مسح الكل
                        </button>
                    </div>
                </div>
                
                <div id="domainHistory" class="domain-history">
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-inbox text-4xl mb-3"></i>
                        <p>لا توجد دومينات محفوظة بعد</p>
                        <p class="text-sm">أضف دومين جديد لبدء التتبع</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Tools -->
        <div class="max-w-6xl mx-auto mt-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">أدوات سريعة</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <button onclick="openDomainChecker()" 
                        class="tool-card bg-white rounded-lg shadow-lg p-4 text-center hover:bg-blue-50">
                    <i class="fas fa-search text-2xl text-blue-500 mb-2"></i>
                    <p class="font-semibold text-gray-800">فحص الدومين</p>
                </button>
                
                <button onclick="openSSLChecker()" 
                        class="tool-card bg-white rounded-lg shadow-lg p-4 text-center hover:bg-green-50">
                    <i class="fas fa-shield-alt text-2xl text-green-500 mb-2"></i>
                    <p class="font-semibold text-gray-800">فحص SSL</p>
                </button>
                
                <button onclick="openSpeedTest()" 
                        class="tool-card bg-white rounded-lg shadow-lg p-4 text-center hover:bg-yellow-50">
                    <i class="fas fa-tachometer-alt text-2xl text-yellow-500 mb-2"></i>
                    <p class="font-semibold text-gray-800">اختبار السرعة</p>
                </button>
                
                <button onclick="openSEOChecker()" 
                        class="tool-card bg-white rounded-lg shadow-lg p-4 text-center hover:bg-purple-50">
                    <i class="fas fa-chart-line text-2xl text-purple-500 mb-2"></i>
                    <p class="font-semibold text-gray-800">فحص SEO</p>
                </button>
            </div>
        </div>
    </main>

    <!-- Add Domain Modal -->
    <div id="addDomainModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-800">إضافة دومين جديد</h3>
                    <button onclick="closeAddDomainModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الدومين:</label>
                        <input type="url" id="newDomainUrl" placeholder="https://your-site.netlify.app"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المشروع:</label>
                        <input type="text" id="newDomainName" placeholder="اسم المشروع"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المنصة:</label>
                        <select id="newDomainPlatform" class="w-full px-4 py-3 border border-gray-300 rounded-lg">
                            <option value="netlify">Netlify</option>
                            <option value="vercel">Vercel</option>
                            <option value="github">GitHub Pages</option>
                            <option value="surge">Surge.sh</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                </div>

                <div class="flex space-x-3 space-x-reverse mt-6">
                    <button onclick="saveDomain()"
                            class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-medium">
                        <i class="fas fa-save mr-2"></i>حفظ
                    </button>
                    <button onclick="closeAddDomainModal()"
                            class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg font-medium">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-16 py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 أداة إنشاء الدومين. صُنعت لتوفير وقتك.</p>
            <p class="mt-2 text-gray-400">
                <i class="fas fa-heart text-red-500"></i>
                أداة شخصية للمطورين
            </p>
        </div>
    </footer>

    <script>
        // Global variables
        let domainHistory = JSON.parse(localStorage.getItem('domainHistory')) || [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Domain Creator Tool loaded');
            loadDomainHistory();

            // Auto-focus QR input when typing
            document.getElementById('qrDomainInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    generateQR('small');
                }
            });
        });

        // Quick Actions
        function openNetlifyDrop() {
            window.open('https://app.netlify.com/drop', '_blank');
            trackAction('netlify_drop_opened');
        }

        function openVercel() {
            window.open('https://vercel.com/new', '_blank');
            trackAction('vercel_opened');
        }

        function openSurge() {
            window.open('https://surge.sh/', '_blank');
            trackAction('surge_opened');
        }

        function openGitHub() {
            window.open('https://pages.github.com/', '_blank');
            trackAction('github_pages_opened');
        }

        // Platform Status
        function checkAllStatus() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري التحديث...';
            button.disabled = true;

            // Simulate status check
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // Show success message
                showNotification('تم تحديث حالة جميع المنصات', 'success');
            }, 2000);
        }

        // QR Generator
        function generateQR(size) {
            const domainInput = document.getElementById('qrDomainInput');
            const domain = domainInput.value.trim();

            if (!domain) {
                showNotification('يرجى إدخال الدومين أولاً', 'error');
                return;
            }

            if (!domain.startsWith('http')) {
                showNotification('يرجى إدخال دومين صحيح يبدأ بـ https://', 'error');
                return;
            }

            const qrResult = document.getElementById('qrResult');
            const qrContainer = document.getElementById('qrContainer');

            // Show loading
            qrContainer.innerHTML = `
                <div class="text-gray-500 p-4">
                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                    <p>جاري توليد QR...</p>
                </div>
            `;
            qrResult.classList.remove('hidden');

            // Determine size
            const qrSize = size === 'large' ? '300x300' : '200x200';

            // Generate QR
            const img = document.createElement('img');
            img.src = `https://api.qrserver.com/v1/create-qr-code/?size=${qrSize}&data=${encodeURIComponent(domain)}`;
            img.alt = 'QR Code';
            img.className = size === 'large' ? 'w-64 h-64' : 'w-48 h-48';
            img.classList.add('border', 'rounded-lg', 'shadow-sm');

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadQRBtn').style.display = 'inline-block';
                document.getElementById('copyQRBtn').style.display = 'inline-block';

                showNotification('تم توليد رمز QR بنجاح', 'success');
                trackAction('qr_generated', { domain, size });
            };

            img.onerror = function() {
                qrContainer.innerHTML = `
                    <div class="text-center p-4">
                        <i class="fas fa-exclamation-triangle text-3xl text-red-500 mb-2"></i>
                        <p class="text-red-600">فشل في توليد QR</p>
                    </div>
                `;
                showNotification('فشل في توليد رمز QR', 'error');
            };
        }

        function downloadQR() {
            const img = document.querySelector('#qrContainer img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'qr-code.png';
                link.href = img.src;
                link.click();

                showNotification('تم تحميل رمز QR', 'success');
                trackAction('qr_downloaded');
            }
        }

        function copyQRLink() {
            const img = document.querySelector('#qrContainer img');
            if (img && navigator.clipboard) {
                navigator.clipboard.writeText(img.src).then(() => {
                    showNotification('تم نسخ رابط QR', 'success');
                    trackAction('qr_link_copied');
                });
            }
        }

        // Domain History Management
        function addDomain() {
            document.getElementById('addDomainModal').classList.remove('hidden');
        }

        function closeAddDomainModal() {
            document.getElementById('addDomainModal').classList.add('hidden');
            // Clear form
            document.getElementById('newDomainUrl').value = '';
            document.getElementById('newDomainName').value = '';
            document.getElementById('newDomainPlatform').value = 'netlify';
        }

        function saveDomain() {
            const url = document.getElementById('newDomainUrl').value.trim();
            const name = document.getElementById('newDomainName').value.trim();
            const platform = document.getElementById('newDomainPlatform').value;

            if (!url || !name) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            if (!url.startsWith('http')) {
                showNotification('يرجى إدخال دومين صحيح', 'error');
                return;
            }

            const domain = {
                id: Date.now(),
                url: url,
                name: name,
                platform: platform,
                createdAt: new Date().toISOString(),
                lastChecked: null,
                status: 'unknown'
            };

            domainHistory.unshift(domain);
            localStorage.setItem('domainHistory', JSON.stringify(domainHistory));

            loadDomainHistory();
            closeAddDomainModal();

            showNotification('تم حفظ الدومين بنجاح', 'success');
            trackAction('domain_saved', { platform, name });
        }

        function loadDomainHistory() {
            const historyContainer = document.getElementById('domainHistory');

            if (domainHistory.length === 0) {
                historyContainer.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-inbox text-4xl mb-3"></i>
                        <p>لا توجد دومينات محفوظة بعد</p>
                        <p class="text-sm">أضف دومين جديد لبدء التتبع</p>
                    </div>
                `;
                return;
            }

            historyContainer.innerHTML = domainHistory.map(domain => `
                <div class="domain-item">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <h4 class="font-semibold text-gray-800">${domain.name}</h4>
                                <span class="bg-${getPlatformColor(domain.platform)}-100 text-${getPlatformColor(domain.platform)}-800 text-xs px-2 py-1 rounded-full mr-2">
                                    ${getPlatformName(domain.platform)}
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 font-mono">${domain.url}</p>
                            <p class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-calendar mr-1"></i>
                                ${new Date(domain.createdAt).toLocaleDateString('ar-SA')}
                            </p>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="openDomain('${domain.url}')"
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                            <button onclick="generateQRForDomain('${domain.url}')"
                                    class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-qrcode"></i>
                            </button>
                            <button onclick="copyDomain('${domain.url}')"
                                    class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button onclick="deleteDomain(${domain.id})"
                                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function getPlatformColor(platform) {
            const colors = {
                'netlify': 'blue',
                'vercel': 'gray',
                'github': 'gray',
                'surge': 'green',
                'other': 'purple'
            };
            return colors[platform] || 'gray';
        }

        function getPlatformName(platform) {
            const names = {
                'netlify': 'Netlify',
                'vercel': 'Vercel',
                'github': 'GitHub',
                'surge': 'Surge',
                'other': 'أخرى'
            };
            return names[platform] || 'غير محدد';
        }

        function openDomain(url) {
            window.open(url, '_blank');
            trackAction('domain_opened', { url });
        }

        function generateQRForDomain(url) {
            document.getElementById('qrDomainInput').value = url;
            generateQR('small');
        }

        function copyDomain(url) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showNotification('تم نسخ الدومين', 'success');
                    trackAction('domain_copied', { url });
                });
            }
        }

        function deleteDomain(id) {
            if (confirm('هل أنت متأكد من حذف هذا الدومين؟')) {
                domainHistory = domainHistory.filter(domain => domain.id !== id);
                localStorage.setItem('domainHistory', JSON.stringify(domainHistory));
                loadDomainHistory();
                showNotification('تم حذف الدومين', 'success');
                trackAction('domain_deleted', { id });
            }
        }

        function clearHistory() {
            if (confirm('هل أنت متأكد من حذف جميع الدومينات؟')) {
                domainHistory = [];
                localStorage.setItem('domainHistory', JSON.stringify(domainHistory));
                loadDomainHistory();
                showNotification('تم مسح جميع الدومينات', 'success');
                trackAction('history_cleared');
            }
        }

        // Quick Tools
        function openDomainChecker() {
            const domain = prompt('أدخل الدومين للفحص:');
            if (domain) {
                window.open(`https://www.whois.net/whois/${domain}`, '_blank');
                trackAction('domain_checker_used', { domain });
            }
        }

        function openSSLChecker() {
            const domain = prompt('أدخل الدومين لفحص SSL:');
            if (domain) {
                window.open(`https://www.ssllabs.com/ssltest/analyze.html?d=${domain}`, '_blank');
                trackAction('ssl_checker_used', { domain });
            }
        }

        function openSpeedTest() {
            const domain = prompt('أدخل الدومين لاختبار السرعة:');
            if (domain) {
                window.open(`https://pagespeed.web.dev/report?url=${domain}`, '_blank');
                trackAction('speed_test_used', { domain });
            }
        }

        function openSEOChecker() {
            const domain = prompt('أدخل الدومين لفحص SEO:');
            if (domain) {
                window.open(`https://www.seoptimer.com/${domain}`, '_blank');
                trackAction('seo_checker_used', { domain });
            }
        }

        // Utility Functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        function trackAction(action, data = {}) {
            // Simple analytics tracking
            const analytics = JSON.parse(localStorage.getItem('toolAnalytics')) || [];
            analytics.push({
                action: action,
                data: data,
                timestamp: new Date().toISOString()
            });

            // Keep only last 100 actions
            if (analytics.length > 100) {
                analytics.splice(0, analytics.length - 100);
            }

            localStorage.setItem('toolAnalytics', JSON.stringify(analytics));
            console.log('Action tracked:', action, data);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + N for new domain
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                addDomain();
            }

            // Ctrl/Cmd + Q for QR focus
            if ((e.ctrlKey || e.metaKey) && e.key === 'q') {
                e.preventDefault();
                document.getElementById('qrDomainInput').focus();
            }

            // Escape to close modal
            if (e.key === 'Escape') {
                closeAddDomainModal();
            }
        });

        console.log('Domain Creator Tool loaded successfully');
    </script>
</body>
</html>
