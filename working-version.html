<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع ملفات HTML - مشاركة سهلة</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .drag-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        .drag-area:hover::before {
            transform: translateX(100%);
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
        }
        .result-section {
            display: none;
            animation: slideInUp 0.6s ease-out;
        }
        .result-section.show {
            display: block;
        }
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .copy-btn {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .copy-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }
        .copy-btn:hover::before {
            left: 100%;
        }
        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .share-btn {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .share-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .pulse-glow {
            animation: pulseGlow 2s infinite;
        }
        @keyframes pulseGlow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            }
            50% {
                box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
            }
        }
        .success-animation {
            animation: successPulse 0.6s ease-out;
        }
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            animation: slideDown 0.3s ease-out;
        }
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold mb-2">
                    <i class="fas fa-upload text-blue-500 mr-3 floating"></i>
                    <span class="gradient-text">رفع ملفات HTML</span>
                </h1>
                <p class="text-gray-600">ارفع ملف HTML واحصل على رابط مباشر + رمز QR للمشاركة</p>
                <div class="mt-3 flex justify-center items-center space-x-4 space-x-reverse text-sm text-gray-500">
                    <span><i class="fas fa-shield-alt text-green-500 mr-1"></i>آمن</span>
                    <span><i class="fas fa-bolt text-yellow-500 mr-1"></i>سريع</span>
                    <span><i class="fas fa-globe text-blue-500 mr-1"></i>عالمي</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <div class="drag-area p-8 rounded-lg text-center pulse-glow" id="dragArea">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4 floating"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">اسحب ملف HTML هنا</h3>
                    <p class="text-gray-500 mb-4">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all transform hover:scale-105">
                        <i class="fas fa-folder-open mr-2"></i>
                        اختر ملف HTML
                    </button>
                    <div class="mt-4 text-xs text-gray-400">
                        الحد الأقصى: 10 ميجابايت | الصيغ المدعومة: .html, .htm
                    </div>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3"></i>
                            <span id="fileName" class="font-medium text-gray-700"></span>
                        </div>
                        <span id="fileSize" class="text-sm text-gray-500"></span>
                    </div>
                </div>
            </div>

            <!-- Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-lg shadow-md p-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center success-animation">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    تم رفع الملف بنجاح!
                </h3>

                <!-- Direct Link -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الرابط المباشر:</label>
                    <div class="flex">
                        <input type="text" id="directLink" readonly 
                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 text-sm">
                        <button onclick="copyLink()" 
                                class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="openLink()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        هذا الرابط يعمل على أي جهاز متصل بالإنترنت ويمكن البحث عنه في جوجل
                    </p>
                </div>

                <!-- QR Code -->
                <div class="mb-6 text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-4">رمز QR:</label>
                    <div class="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg" id="qrContainer">
                        <div class="text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري توليد رمز QR...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg mr-2">
                            <i class="fas fa-download mr-2"></i>
                            تحميل رمز QR
                        </button>
                        <button onclick="regenerateQR()" id="regenerateBtn" style="display:none;"
                                class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-redo mr-2"></i>
                            إعادة توليد
                        </button>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="text-center">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">مشاركة الرابط:</h4>
                    <div class="flex justify-center space-x-4 space-x-reverse flex-wrap gap-2">
                        <button onclick="shareWhatsApp()" 
                                class="share-btn bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-whatsapp mr-2"></i>
                            واتساب
                        </button>
                        <button onclick="shareTwitter()" 
                                class="share-btn bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-twitter mr-2"></i>
                            تويتر
                        </button>
                        <button onclick="shareLinkedIn()" 
                                class="share-btn bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-linkedin mr-2"></i>
                            لينكدإن
                        </button>
                        <button onclick="shareFacebook()" 
                                class="share-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-facebook mr-2"></i>
                            فيسبوك
                        </button>
                    </div>
                    
                    <!-- View File Button -->
                    <div class="mt-6">
                        <button onclick="viewFile()" 
                                class="share-btn bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-medium">
                            <i class="fas fa-eye mr-2"></i>
                            عرض الملف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 رفع ملفات HTML. جميع الحقوق محفوظة.</p>
                <div class="mt-4 space-x-4 space-x-reverse">
                    <a href="#" class="hover:text-blue-500 transition-colors">سياسة الخصوصية</a>
                    <a href="#" class="hover:text-blue-500 transition-colors">شروط الاستخدام</a>
                    <a href="#" class="hover:text-blue-500 transition-colors">تواصل معنا</a>
                    <a href="#" class="hover:text-blue-500 transition-colors">GitHub</a>
                </div>
                <p class="mt-4 text-sm text-gray-500">
                    <i class="fas fa-heart text-red-500"></i>
                    صُنع بحب لخدمة المطورين العرب
                </p>
            </div>
        </div>
    </footer>

    <script>
        console.log('Script loaded successfully');
        
        // Configuration
        const DOMAIN = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '');
        
        // Global variables
        let uploadedFiles = JSON.parse(localStorage.getItem('uploadedFiles')) || {};
        let currentFileId = null;
        let currentFile = null;

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const resultSection = document.getElementById('resultSection');
        const directLink = document.getElementById('directLink');
        const qrContainer = document.getElementById('qrContainer');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up event listeners');
            setupEventListeners();
            checkViewFromURL();
        });

        function setupEventListeners() {
            console.log('Setting up event listeners');
            
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);
            
            // File input change
            fileInput.addEventListener('change', handleFileSelect);
            
            // Click on drag area
            dragArea.addEventListener('click', () => {
                console.log('Drag area clicked');
                fileInput.click();
            });
            
            console.log('Event listeners set up successfully');
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                console.log('File dropped:', files[0].name);
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                console.log('File selected:', file.name);
                handleFile(file);
            }
        }

        function handleFile(file) {
            console.log('Processing file:', file.name, 'Size:', file.size);
            
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                alert('يرجى اختيار ملف HTML فقط (.html أو .htm)');
                return;
            }
            
            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
                return;
            }
            
            // Show file info
            showFileInfo(file);
            
            // Process file
            processFile(file);
        }

        function showFileInfo(file) {
            console.log('Showing file info');
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function processFile(file) {
            console.log('Reading file content...');
            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    console.log('File read successfully');
                    const content = e.target.result;
                    const fileId = generateFileId();

                    console.log('Generated file ID:', fileId);

                    // Store file in localStorage
                    uploadedFiles[fileId] = {
                        name: file.name,
                        content: content,
                        uploadDate: new Date().toISOString(),
                        size: file.size,
                        views: 0
                    };

                    localStorage.setItem('uploadedFiles', JSON.stringify(uploadedFiles));
                    console.log('File stored in localStorage');

                    // Generate link and show result
                    currentFileId = fileId;
                    currentFile = uploadedFiles[fileId];
                    showResult(fileId);

                } catch (error) {
                    console.error('Error processing file:', error);
                    alert('حدث خطأ في معالجة الملف. يرجى المحاولة مرة أخرى.');
                }
            };

            reader.onerror = function() {
                console.error('Error reading file');
                alert('حدث خطأ في قراءة الملف. يرجى المحاولة مرة أخرى.');
            };

            reader.readAsText(file, 'UTF-8');
        }

        function generateFileId() {
            return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function showResult(fileId) {
            try {
                console.log('Showing result for file ID:', fileId);

                // Create the file URL
                const fileUrl = DOMAIN + '?view=' + fileId;
                console.log('Generated URL:', fileUrl);

                // Set the direct link
                directLink.value = fileUrl;

                // Show result section
                resultSection.classList.add('show');

                // Generate QR code
                setTimeout(() => {
                    generateQRCode(fileUrl);
                }, 100);

                // Scroll to result
                setTimeout(() => {
                    resultSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }, 200);

                console.log('Result shown successfully');

            } catch (error) {
                console.error('Error showing result:', error);
                alert('حدث خطأ في عرض النتائج. يرجى المحاولة مرة أخرى.');
            }
        }

        function generateQRCode(url) {
            console.log('Generating QR for URL:', url);

            // Method 1: Using Google Charts API
            const img = document.createElement('img');
            const encodedUrl = encodeURIComponent(url);
            img.src = `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodedUrl}`;
            img.alt = 'QR Code';
            img.className = 'w-48 h-48 border rounded-lg shadow-sm';

            img.onload = function() {
                console.log('QR Code generated successfully');
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('regenerateBtn').style.display = 'inline-block';
            };

            img.onerror = function() {
                console.log('Google Charts failed, trying alternative...');
                // Method 2: Using QR Server API
                const img2 = document.createElement('img');
                img2.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedUrl}`;
                img2.alt = 'QR Code';
                img2.className = 'w-48 h-48 border rounded-lg shadow-sm';

                img2.onload = function() {
                    console.log('QR Code generated with alternative service');
                    qrContainer.innerHTML = '';
                    qrContainer.appendChild(img2);
                    document.getElementById('downloadBtn').style.display = 'inline-block';
                    document.getElementById('regenerateBtn').style.display = 'inline-block';
                };

                img2.onerror = function() {
                    console.log('All QR services failed');
                    qrContainer.innerHTML = `
                        <div class="text-center p-6">
                            <i class="fas fa-qrcode text-6xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">لا يمكن توليد رمز QR تلقائياً</p>
                            <p class="text-sm text-gray-500 mb-4">يمكنك نسخ الرابط أعلاه واستخدامه في أي موقع لتوليد رمز QR</p>
                            <a href="https://www.qr-code-generator.com/" target="_blank"
                               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg inline-block">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                إنشاء رمز QR يدوياً
                            </a>
                        </div>
                    `;
                };
            };
        }

        function regenerateQR() {
            if (currentFileId) {
                const fileUrl = DOMAIN + '?view=' + currentFileId;
                qrContainer.innerHTML = `
                    <div class="text-gray-500 p-6">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>جاري إعادة توليد رمز QR...</p>
                    </div>
                `;
                document.getElementById('downloadBtn').style.display = 'none';
                document.getElementById('regenerateBtn').style.display = 'none';

                setTimeout(() => {
                    generateQRCode(fileUrl);
                }, 500);
            }
        }

        function downloadQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'qr-code.png';
                link.href = img.src;
                link.click();
                showNotification('تم تحميل رمز QR!', 'success');
            } else {
                alert('لا يوجد رمز QR للتحميل');
            }
        }

        function copyLink() {
            const linkInput = document.getElementById('directLink');

            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(linkInput.value).then(() => {
                    showCopyFeedback();
                    showNotification('تم نسخ الرابط!', 'success');
                }).catch(() => {
                    fallbackCopy();
                });
            } else {
                fallbackCopy();
            }

            function fallbackCopy() {
                linkInput.select();
                linkInput.setSelectionRange(0, 99999);
                document.execCommand('copy');
                showCopyFeedback();
                showNotification('تم نسخ الرابط!', 'success');
            }

            function showCopyFeedback() {
                const button = event.target.closest('button');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.remove('bg-blue-500');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-blue-500');
                }, 2000);
            }
        }

        function openLink() {
            window.open(directLink.value, '_blank');
            showNotification('تم فتح الرابط!', 'info');
        }

        // Share functions
        function shareWhatsApp() {
            const url = encodeURIComponent(directLink.value);
            const text = encodeURIComponent('شاهد هذا الملف: ');
            window.open(`https://wa.me/?text=${text}${url}`, '_blank');
        }

        function shareTwitter() {
            const url = encodeURIComponent(directLink.value);
            const text = encodeURIComponent('شاهد هذا الملف: ');
            window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
        }

        function shareLinkedIn() {
            const url = encodeURIComponent(directLink.value);
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
        }

        function shareFacebook() {
            const url = encodeURIComponent(directLink.value);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }

        function viewFile() {
            if (currentFile) {
                const blob = new Blob([currentFile.content], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                window.open(url, '_blank');

                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 1000);
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                'bg-blue-500'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // Check if viewing a file from URL
        function checkViewFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const viewFileId = urlParams.get('view');

            if (viewFileId) {
                console.log('Viewing file from URL:', viewFileId);
                const file = uploadedFiles[viewFileId];
                if (file) {
                    // Increment view count
                    file.views = (file.views || 0) + 1;
                    uploadedFiles[viewFileId] = file;
                    localStorage.setItem('uploadedFiles', JSON.stringify(uploadedFiles));

                    // Show the file content
                    document.body.innerHTML = `
                        <div class="min-h-screen bg-gray-50">
                            <header class="bg-white shadow-sm border-b p-4">
                                <div class="container mx-auto">
                                    <div class="flex items-center justify-between flex-wrap gap-4">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-code text-blue-500 text-2xl mr-3"></i>
                                            <div>
                                                <h1 class="text-xl font-bold text-gray-800">${file.name}</h1>
                                                <p class="text-sm text-gray-500">
                                                    <i class="fas fa-eye mr-1"></i>
                                                    ${file.views} مشاهدة •
                                                    <i class="fas fa-calendar mr-1"></i>
                                                    ${new Date(file.uploadDate).toLocaleDateString('ar-SA')}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button onclick="window.history.back()"
                                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                                                <i class="fas fa-arrow-right mr-2"></i>
                                                رجوع
                                            </button>
                                            <button onclick="downloadCurrentFile()"
                                                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                                <i class="fas fa-download mr-2"></i>
                                                تحميل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </header>
                            <main class="container mx-auto p-4">
                                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                                    <div class="bg-gray-100 px-4 py-2 border-b">
                                        <p class="text-sm text-gray-600">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            يتم عرض الملف في بيئة آمنة ومعزولة
                                        </p>
                                    </div>
                                    <iframe srcdoc="${file.content.replace(/"/g, '&quot;')}"
                                            class="w-full h-screen border-0"></iframe>
                                </div>
                            </main>
                            <footer class="bg-white border-t p-4 text-center">
                                <p class="text-gray-600">
                                    <i class="fas fa-shield-alt mr-1"></i>
                                    تم عرض هذا الملف بواسطة
                                    <a href="${DOMAIN}" class="text-blue-500 hover:underline">موقع رفع ملفات HTML</a>
                                </p>
                            </footer>
                        </div>
                        <script>
                            function downloadCurrentFile() {
                                const blob = new Blob([\`${file.content.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`], { type: 'text/html' });
                                const url = URL.createObjectURL(blob);
                                const link = document.createElement('a');
                                link.href = url;
                                link.download = '${file.name}';
                                link.click();
                                URL.revokeObjectURL(url);
                            }
                        </script>
                    `;
                    document.title = `${file.name} - موقع رفع ملفات HTML`;
                } else {
                    // File not found
                    document.body.innerHTML = `
                        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
                            <div class="text-center p-8">
                                <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-6"></i>
                                <h1 class="text-3xl font-bold text-gray-800 mb-4">الملف غير موجود</h1>
                                <p class="text-gray-600 mb-8">الملف المطلوب غير موجود أو تم حذفه</p>
                                <a href="${DOMAIN}" class="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg inline-block">
                                    <i class="fas fa-home mr-2"></i>
                                    العودة للصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    `;
                    document.title = 'الملف غير موجود - موقع رفع ملفات HTML';
                }
            }
        }
    </script>
</body>
</html>
