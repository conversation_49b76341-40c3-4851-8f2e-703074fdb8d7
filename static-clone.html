<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استضافة مواقع HTML - رفع واستضافة فورية</title>
    <meta name="description" content="ارفع ملف HTML أو ZIP واحصل على رابط مباشر فوراً - استضافة مجانية وسريعة">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌐</text></svg>">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .upload-zone {
            border: 3px dashed #e2e8f0;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .upload-zone.dragover {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            transform: scale(1.02);
        }
        .upload-zone:hover {
            border-color: #6366f1;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        }
        .result-card {
            display: none;
            animation: slideUp 0.5s ease-out;
        }
        .result-card.show {
            display: block;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .feature-card {
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fas fa-cloud-upload-alt text-6xl mb-4 pulse-animation"></i>
                </div>
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    استضافة مواقع HTML
                </h1>
                <p class="text-xl md:text-2xl mb-6 opacity-90">
                    ارفع ملف HTML أو ZIP واحصل على رابط مباشر فوراً
                </p>
                <p class="text-lg opacity-80">
                    <i class="fas fa-bolt mr-2"></i>
                    سريع • مجاني • آمن
                </p>
            </div>
        </div>
    </header>

    <!-- Main Upload Section -->
    <main class="container mx-auto px-4 py-12">
        <!-- Upload Zone -->
        <div class="max-w-4xl mx-auto mb-12">
            <div class="upload-zone rounded-2xl p-12 text-center" id="uploadZone">
                <div class="mb-6">
                    <i class="fas fa-cloud-upload-alt text-8xl text-gray-400 mb-6"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-800 mb-4">
                    اسحب ملفاتك هنا
                </h2>
                <p class="text-xl text-gray-600 mb-6">
                    أو اضغط لاختيار الملفات
                </p>
                <input type="file" id="fileInput" accept=".html,.htm,.zip" multiple class="hidden">
                <button onclick="document.getElementById('fileInput').click()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105">
                    <i class="fas fa-folder-open mr-3"></i>
                    اختر الملفات
                </button>
                <div class="mt-6 text-sm text-gray-500">
                    <p>الصيغ المدعومة: HTML, HTM, ZIP</p>
                    <p>الحد الأقصى: 50 ميجابايت لكل ملف</p>
                </div>
            </div>
        </div>

        <!-- Processing Animation -->
        <div id="processingSection" class="max-w-2xl mx-auto mb-12 hidden">
            <div class="bg-white rounded-2xl shadow-lg p-8 text-center">
                <div class="mb-6">
                    <i class="fas fa-cog fa-spin text-6xl text-blue-500"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-4">جاري المعالجة...</h3>
                <p class="text-gray-600 mb-6">نقوم بمعالجة ملفاتك وإنشاء الروابط</p>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="bg-blue-600 h-3 rounded-full transition-all duration-1000" id="progressBar" style="width: 0%"></div>
                </div>
                <p class="text-sm text-gray-500 mt-3" id="progressText">0%</p>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="max-w-4xl mx-auto result-card">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <div class="text-center mb-8">
                    <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i>
                    <h3 class="text-3xl font-bold text-gray-800 mb-2">تم الرفع بنجاح!</h3>
                    <p class="text-gray-600">ملفاتك متاحة الآن على الإنترنت</p>
                </div>

                <!-- Files List -->
                <div id="filesList" class="space-y-4 mb-8">
                    <!-- Files will be added here dynamically -->
                </div>

                <!-- QR Code Section -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-gray-800 mb-4">رمز QR للمشاركة السريعة</h4>
                    <div class="inline-block p-4 bg-gray-50 rounded-xl" id="qrContainer">
                        <div class="text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري توليد رمز QR...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" id="downloadQRBtn" style="display:none;"
                                class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg mr-3">
                            <i class="fas fa-download mr-2"></i>
                            تحميل QR
                        </button>
                        <button onclick="shareFiles()" 
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg">
                            <i class="fas fa-share mr-2"></i>
                            مشاركة
                        </button>
                    </div>
                </div>

                <!-- Upload Another -->
                <div class="text-center">
                    <button onclick="resetUpload()" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg">
                        <i class="fas fa-plus mr-2"></i>
                        رفع ملفات أخرى
                    </button>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="max-w-6xl mx-auto mt-20">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">لماذا تختارنا؟</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card bg-white rounded-xl shadow-lg p-8 text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-bolt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">سرعة فائقة</h3>
                    <p class="text-gray-600">رفع ونشر فوري خلال ثوانٍ معدودة</p>
                </div>
                
                <div class="feature-card bg-white rounded-xl shadow-lg p-8 text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-shield-alt text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">آمان كامل</h3>
                    <p class="text-gray-600">SSL مجاني وحماية متقدمة لملفاتك</p>
                </div>
                
                <div class="feature-card bg-white rounded-xl shadow-lg p-8 text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-globe text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">وصول عالمي</h3>
                    <p class="text-gray-600">CDN سريع يصل لجميع أنحاء العالم</p>
                </div>
            </div>
        </div>

        <!-- How It Works -->
        <div class="max-w-4xl mx-auto mt-20">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">كيف يعمل؟</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl font-bold text-white">1</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">ارفع الملف</h3>
                    <p class="text-gray-600">اسحب ملف HTML أو ZIP إلى المنطقة المخصصة</p>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl font-bold text-white">2</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">معالجة تلقائية</h3>
                    <p class="text-gray-600">نقوم بمعالجة الملف وإنشاء رابط فريد</p>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl font-bold text-white">3</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">شارك الرابط</h3>
                    <p class="text-gray-600">احصل على رابط مباشر ورمز QR للمشاركة</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-20">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">استضافة HTML</h3>
                    <p class="text-gray-400">الحل الأمثل لاستضافة مواقعك الثابتة بسرعة وأمان</p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">الخدمات</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">استضافة HTML</a></li>
                        <li><a href="#" class="hover:text-white">رفع الملفات</a></li>
                        <li><a href="#" class="hover:text-white">توليد QR</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">الدعم</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">مركز المساعدة</a></li>
                        <li><a href="#" class="hover:text-white">تواصل معنا</a></li>
                        <li><a href="#" class="hover:text-white">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">تابعنا</h4>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="text-gray-400 hover:text-white text-xl">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white text-xl">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white text-xl">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 استضافة HTML. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Configuration
        const DOMAIN = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '');
        
        // Global variables
        let uploadedFiles = JSON.parse(localStorage.getItem('staticFiles')) || {};
        let currentUploadId = null;

        // DOM Elements
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const processingSection = document.getElementById('processingSection');
        const resultsSection = document.getElementById('resultsSection');
        const filesList = document.getElementById('filesList');
        const qrContainer = document.getElementById('qrContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            checkViewFromURL();
        });

        function setupEventListeners() {
            // Drag and drop events
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', handleDrop);
            uploadZone.addEventListener('click', () => fileInput.click());
            
            // File input change
            fileInput.addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadZone.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                processFiles(files);
            }
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            if (files.length > 0) {
                processFiles(files);
            }
        }

        function processFiles(files) {
            // Validate files
            const validFiles = files.filter(file => {
                const isValidType = file.name.toLowerCase().match(/\.(html|htm|zip)$/);
                const isValidSize = file.size <= 50 * 1024 * 1024; // 50MB

                if (!isValidType) {
                    alert(`الملف ${file.name} غير مدعوم. يرجى اختيار ملفات HTML أو ZIP فقط.`);
                    return false;
                }

                if (!isValidSize) {
                    alert(`الملف ${file.name} كبير جداً. الحد الأقصى 50 ميجابايت.`);
                    return false;
                }

                return true;
            });

            if (validFiles.length === 0) return;

            // Start processing
            showProcessing();

            // Simulate processing with progress
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                updateProgress(progress);

                if (progress >= 90) {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        updateProgress(100);
                        setTimeout(() => {
                            processFilesComplete(validFiles);
                        }, 500);
                    }, 500);
                }
            }, 200);
        }

        function showProcessing() {
            uploadZone.style.display = 'none';
            processingSection.classList.remove('hidden');
            resultsSection.classList.remove('show');
        }

        function updateProgress(percent) {
            progressBar.style.width = percent + '%';
            progressText.textContent = Math.round(percent) + '%';
        }

        function processFilesComplete(files) {
            const uploadId = generateUploadId();
            currentUploadId = uploadId;

            const processedFiles = [];

            files.forEach((file, index) => {
                const fileId = generateFileId();
                const fileName = file.name;
                const fileUrl = `${DOMAIN}?file=${fileId}`;

                // Read file content
                const reader = new FileReader();
                reader.onload = function(e) {
                    const fileData = {
                        id: fileId,
                        name: fileName,
                        content: e.target.result,
                        size: file.size,
                        type: file.type,
                        uploadDate: new Date().toISOString(),
                        url: fileUrl,
                        views: 0
                    };

                    // Store in localStorage
                    uploadedFiles[fileId] = fileData;
                    localStorage.setItem('staticFiles', JSON.stringify(uploadedFiles));

                    processedFiles.push(fileData);

                    // If all files processed, show results
                    if (processedFiles.length === files.length) {
                        showResults(processedFiles);
                    }
                };

                if (file.name.toLowerCase().endsWith('.zip')) {
                    reader.readAsArrayBuffer(file);
                } else {
                    reader.readAsText(file);
                }
            });
        }

        function showResults(files) {
            processingSection.classList.add('hidden');

            // Clear previous results
            filesList.innerHTML = '';

            // Add files to results
            files.forEach(file => {
                const fileCard = createFileCard(file);
                filesList.appendChild(fileCard);
            });

            // Generate QR for first file
            if (files.length > 0) {
                generateQRCode(files[0].url);
            }

            // Show results
            resultsSection.classList.add('show');
        }

        function createFileCard(file) {
            const card = document.createElement('div');
            card.className = 'bg-gray-50 rounded-xl p-6 border border-gray-200';

            const fileIcon = file.name.toLowerCase().endsWith('.zip') ? 'fa-file-archive' : 'fa-file-code';
            const fileSize = formatFileSize(file.size);

            card.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas ${fileIcon} text-xl text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">${file.name}</h4>
                            <p class="text-sm text-gray-500">${fileSize} • تم الرفع الآن</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="copyFileUrl('${file.url}')"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-copy mr-1"></i>
                            نسخ الرابط
                        </button>
                        <button onclick="openFile('${file.url}')"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            فتح
                        </button>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-white rounded-lg border">
                    <p class="text-sm text-gray-600 mb-2">الرابط المباشر:</p>
                    <code class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded break-all">${file.url}</code>
                </div>
            `;

            return card;
        }

        function generateUploadId() {
            return 'upload_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function generateFileId() {
            return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function generateQRCode(url) {
            console.log('Generating QR for:', url);

            // Using Google Charts API
            const img = document.createElement('img');
            const encodedUrl = encodeURIComponent(url);
            img.src = `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodedUrl}`;
            img.alt = 'QR Code';
            img.className = 'w-48 h-48 border rounded-lg shadow-sm';

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadQRBtn').style.display = 'inline-block';
                console.log('QR Code generated successfully');
            };

            img.onerror = function() {
                console.log('QR generation failed, trying alternative...');
                // Alternative QR service
                const img2 = document.createElement('img');
                img2.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedUrl}`;
                img2.alt = 'QR Code';
                img2.className = 'w-48 h-48 border rounded-lg shadow-sm';

                img2.onload = function() {
                    qrContainer.innerHTML = '';
                    qrContainer.appendChild(img2);
                    document.getElementById('downloadQRBtn').style.display = 'inline-block';
                };

                img2.onerror = function() {
                    qrContainer.innerHTML = `
                        <div class="text-center p-6">
                            <i class="fas fa-qrcode text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600">لا يمكن توليد رمز QR</p>
                        </div>
                    `;
                };
            };
        }

        function copyFileUrl(url) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showNotification('تم نسخ الرابط!', 'success');
                });
            } else {
                // Fallback
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ الرابط!', 'success');
            }
        }

        function openFile(url) {
            window.open(url, '_blank');
        }

        function downloadQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'qr-code.png';
                link.href = img.src;
                link.click();
            }
        }

        function shareFiles() {
            const files = Array.from(filesList.querySelectorAll('code')).map(code => code.textContent);

            if (navigator.share && files.length > 0) {
                navigator.share({
                    title: 'ملفاتي المرفوعة',
                    text: 'شاهد ملفاتي:',
                    url: files[0]
                });
            } else {
                // Fallback to copy first URL
                if (files.length > 0) {
                    copyFileUrl(files[0]);
                }
            }
        }

        function resetUpload() {
            uploadZone.style.display = 'block';
            processingSection.classList.add('hidden');
            resultsSection.classList.remove('show');
            fileInput.value = '';
            updateProgress(0);
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 left-1/2 transform -translate-x-1/2 z-50 px-6 py-3 rounded-lg text-white font-medium ${
                type === 'success' ? 'bg-green-500' : 'bg-blue-500'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Check if viewing a file from URL
        function checkViewFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const fileId = urlParams.get('file');

            if (fileId) {
                const file = uploadedFiles[fileId];
                if (file) {
                    // Increment view count
                    file.views = (file.views || 0) + 1;
                    uploadedFiles[fileId] = file;
                    localStorage.setItem('staticFiles', JSON.stringify(uploadedFiles));

                    // Show the file content
                    if (file.name.toLowerCase().endsWith('.zip')) {
                        showZipViewer(file);
                    } else {
                        showHTMLViewer(file);
                    }
                } else {
                    showFileNotFound();
                }
            }
        }

        function showHTMLViewer(file) {
            document.body.innerHTML = `
                <div class="min-h-screen bg-gray-50">
                    <header class="bg-white shadow-sm border-b p-4">
                        <div class="container mx-auto flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-file-code text-blue-500 text-2xl mr-3"></i>
                                <div>
                                    <h1 class="text-xl font-bold text-gray-800">${file.name}</h1>
                                    <p class="text-sm text-gray-500">
                                        <i class="fas fa-eye mr-1"></i>
                                        ${file.views} مشاهدة •
                                        ${formatFileSize(file.size)}
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button onclick="window.history.back()"
                                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                                    <i class="fas fa-arrow-right mr-2"></i>
                                    رجوع
                                </button>
                                <button onclick="downloadCurrentFile()"
                                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                    <i class="fas fa-download mr-2"></i>
                                    تحميل
                                </button>
                            </div>
                        </div>
                    </header>
                    <main class="container mx-auto p-4">
                        <div class="bg-white rounded-lg shadow-md overflow-hidden">
                            <iframe srcdoc="${file.content.replace(/"/g, '&quot;')}"
                                    class="w-full h-screen border-0"></iframe>
                        </div>
                    </main>
                </div>
                <script>
                    function downloadCurrentFile() {
                        const blob = new Blob([\`${file.content.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = '${file.name}';
                        link.click();
                        URL.revokeObjectURL(url);
                    }
                </script>
            `;
            document.title = `${file.name} - استضافة HTML`;
        }

        function showZipViewer(file) {
            document.body.innerHTML = `
                <div class="min-h-screen bg-gray-50">
                    <header class="bg-white shadow-sm border-b p-4">
                        <div class="container mx-auto flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-file-archive text-purple-500 text-2xl mr-3"></i>
                                <div>
                                    <h1 class="text-xl font-bold text-gray-800">${file.name}</h1>
                                    <p class="text-sm text-gray-500">
                                        <i class="fas fa-eye mr-1"></i>
                                        ${file.views} مشاهدة •
                                        ${formatFileSize(file.size)}
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button onclick="window.history.back()"
                                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                                    <i class="fas fa-arrow-right mr-2"></i>
                                    رجوع
                                </button>
                                <button onclick="downloadCurrentFile()"
                                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                    <i class="fas fa-download mr-2"></i>
                                    تحميل
                                </button>
                            </div>
                        </div>
                    </header>
                    <main class="container mx-auto p-4">
                        <div class="bg-white rounded-lg shadow-md p-8 text-center">
                            <i class="fas fa-file-archive text-6xl text-purple-500 mb-6"></i>
                            <h2 class="text-2xl font-bold text-gray-800 mb-4">ملف مضغوط</h2>
                            <p class="text-gray-600 mb-6">هذا ملف ZIP مضغوط. يمكنك تحميله لاستخراج محتوياته.</p>
                            <button onclick="downloadCurrentFile()"
                                    class="bg-purple-500 hover:bg-purple-600 text-white px-8 py-3 rounded-lg">
                                <i class="fas fa-download mr-2"></i>
                                تحميل الملف
                            </button>
                        </div>
                    </main>
                </div>
                <script>
                    function downloadCurrentFile() {
                        const byteCharacters = atob('${btoa(String.fromCharCode(...new Uint8Array(file.content)))}');
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        const blob = new Blob([byteArray], { type: 'application/zip' });
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = '${file.name}';
                        link.click();
                        URL.revokeObjectURL(url);
                    }
                </script>
            `;
            document.title = `${file.name} - استضافة HTML`;
        }

        function showFileNotFound() {
            document.body.innerHTML = `
                <div class="min-h-screen bg-gray-50 flex items-center justify-center">
                    <div class="text-center p-8">
                        <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-6"></i>
                        <h1 class="text-3xl font-bold text-gray-800 mb-4">الملف غير موجود</h1>
                        <p class="text-gray-600 mb-8">الملف المطلوب غير موجود أو تم حذفه</p>
                        <a href="${DOMAIN}" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg inline-block">
                            <i class="fas fa-home mr-2"></i>
                            العودة للصفحة الرئيسية
                        </a>
                    </div>
                </div>
            `;
            document.title = 'الملف غير موجود - استضافة HTML';
        }
    </script>
</body>
</html>
