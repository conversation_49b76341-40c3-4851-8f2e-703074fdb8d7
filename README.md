# موقع رفع ملفات HTML 📤

موقع ويب بسيط يمكّن المستخدم من رفع ملفات HTML والحصول على رابط مباشر مع رمز QR للمشاركة.

## ✨ الميزات

### 🎯 الوظائف الأساسية
- **رفع الملفات**: سحب وإفلات أو اختيار ملف HTML
- **التحقق من صحة الملف**: يقبل فقط ملفات .html و .htm
- **حد أقصى لحجم الملف**: 10 ميجابايت
- **تخزين محلي**: يحفظ الملفات في localStorage

### 🔗 الروابط والمشاركة
- **رابط مباشر**: يتم توليد رابط فريد لكل ملف
- **رمز QR**: توليد رمز QR قابل للتحميل
- **أزرار المشاركة**: واتساب، تويتر، لينكدإن، فيسبوك
- **نسخ الرابط**: زر نسخ سريع مع تأكيد بصري

### 👁️ عرض الملفات
- **معاينة مباشرة**: عرض الملف كما يظهر في المتصفح
- **عرض الكود**: إمكانية عرض الكود المصدري
- **فتح في تبويب جديد**: لعرض الملف بشكل منفصل
- **تحميل الملف**: إمكانية تحميل الملف الأصلي

## 🚀 كيفية الاستخدام

### 1. فتح الموقع
افتح ملف `index.html` في أي متصفح ويب حديث.

### 2. رفع ملف HTML
- **الطريقة الأولى**: اسحب ملف HTML وأفلته في المنطقة المخصصة
- **الطريقة الثانية**: اضغط على زر "اختر ملف HTML" واختر الملف

### 3. الحصول على النتائج
بعد رفع الملف بنجاح، ستحصل على:
- **رابط مباشر** يمكن مشاركته
- **رمز QR** قابل للتحميل
- **أزرار مشاركة** لوسائل التواصل الاجتماعي

### 4. عرض الملف
- اضغط على زر "عرض الملف" لمعاينة المحتوى
- يمكنك التبديل بين العرض المرئي وعرض الكود
- إمكانية فتح الملف في تبويب جديد أو تحميله

## 🎨 التصميم والتقنيات

### التقنيات المستخدمة
- **HTML5**: هيكل حديث ومتوافق
- **Tailwind CSS**: تصميم سريع ومرن
- **JavaScript**: وظائف تفاعلية كاملة
- **QRCode.js**: مكتبة توليد رمز QR
- **Font Awesome**: أيقونات جميلة
- **localStorage**: تخزين محلي للملفات

### التصميم
- **واجهة عربية**: دعم كامل للغة العربية مع RTL
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **ألوان محايدة**: رمادي، أبيض، أزرق خفيف
- **تأثيرات تفاعلية**: hover effects وانتقالات سلسة

## 📁 الملفات

- `index.html` - الملف الرئيسي للموقع (يحتوي على كل شيء)
- `test-file.html` - ملف تجريبي لاختبار الموقع
- `README.md` - هذا الملف

## 🔧 متطلبات التشغيل

- متصفح ويب حديث يدعم:
  - HTML5
  - CSS3
  - JavaScript ES6+
  - localStorage
  - Canvas API (لرمز QR)

## 🛠️ التخصيص

يمكنك تخصيص الموقع بسهولة:

### تغيير الألوان
ابحث عن الألوان في CSS وغيرها حسب تفضيلك:
```css
/* الألوان الأساسية */
bg-blue-500    /* الأزرق الأساسي */
bg-green-500   /* الأخضر للنجاح */
bg-purple-500  /* البنفسجي للمميزات */
```

### تغيير النصوص
جميع النصوص موجودة في HTML ويمكن تعديلها مباشرة.

### إضافة ميزات جديدة
يمكنك إضافة ميزات جديدة في قسم JavaScript.

## 🔒 الأمان والخصوصية

- **التخزين المحلي**: الملفات تُحفظ في متصفحك فقط
- **لا توجد خوادم خارجية**: كل شيء يعمل محلياً
- **التحقق من نوع الملف**: يقبل فقط ملفات HTML
- **حد أقصى لحجم الملف**: 10 ميجابايت لمنع إساءة الاستخدام

## 🐛 استكشاف الأخطاء

### المشاكل الشائعة وحلولها

1. **لا يظهر رمز QR**
   - تأكد من تحميل مكتبة QRCode.js
   - تحقق من وجود اتصال بالإنترنت

2. **لا يعمل نسخ الرابط**
   - تأكد من أن الموقع يعمل عبر HTTPS أو localhost
   - جرب المتصفحات الحديثة

3. **لا يتم حفظ الملفات**
   - تأكد من تمكين localStorage في المتصفح
   - تحقق من مساحة التخزين المتاحة

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يمكنك:
- فتح issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الكود المصدري للتعديل

---

**ملاحظة**: هذا الموقع مصمم للاستخدام المحلي والتجريبي. للاستخدام في الإنتاج، يُنصح بإضافة خادم خلفي وقاعدة بيانات مناسبة.
