<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Auto Uploader - ذكاء اصطناعي يرفع تلقائياً</title>
    <meta name="description" content="ذكاء اصطناعي يفتح static.app ويرفع ملفك تلقائياً">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .ai-gradient {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .drag-area.dragover {
            border-color: #8b5cf6;
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            transform: scale(1.02);
        }
        .ai-working {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .ai-step {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
            opacity: 0.5;
            transition: all 0.3s ease;
        }
        .ai-step.active {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            opacity: 1;
            transform: scale(1.05);
        }
        .ai-step.completed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            opacity: 1;
        }
        .iframe-container {
            border: 3px solid #8b5cf6;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
            position: relative;
        }
        .ai-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(139, 92, 246, 0.9);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10;
            transition: all 0.3s ease;
        }
        .ai-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
        .typing-animation {
            animation: typing 1.5s infinite;
        }
        @keyframes typing {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        .ai-brain {
            animation: pulse 2s infinite;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        .ai-cursor {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff0000;
            border-radius: 50%;
            pointer-events: none;
            z-index: 20;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="hero-gradient text-white">
        <div class="container mx-auto px-4 py-16">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    <i class="fas fa-robot mr-3 ai-brain"></i>
                    AI Auto Uploader
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    ذكاء اصطناعي يفتح static.app ويرفع ملفك تلقائياً
                </p>
                <div class="flex justify-center items-center space-x-8 space-x-reverse text-lg mb-8">
                    <span><i class="fas fa-magic text-yellow-300 mr-2"></i>تلقائي 100%</span>
                    <span><i class="fas fa-robot text-purple-300 mr-2"></i>AI يعمل كل شيء</span>
                    <span><i class="fas fa-bolt text-green-300 mr-2"></i>بدون تدخل</span>
                    <span><i class="fas fa-check text-blue-300 mr-2"></i>نتيجة مضمونة</span>
                </div>
                
                <div class="bg-white bg-opacity-10 rounded-lg p-6 max-w-4xl mx-auto">
                    <h3 class="text-xl font-bold mb-4">🤖 كيف يعمل الذكاء الاصطناعي؟</h3>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-upload text-xl mb-2"></i>
                            <p><strong>1. ترفع الملف</strong></p>
                            <p class="opacity-80">للذكاء الاصطناعي</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-external-link-alt text-xl mb-2"></i>
                            <p><strong>2. يفتح static.app</strong></p>
                            <p class="opacity-80">في نافذة جديدة</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-mouse-pointer text-xl mb-2"></i>
                            <p><strong>3. يتحكم في الصفحة</strong></p>
                            <p class="opacity-80">يحرك الماوس ويضغط</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-cloud-upload-alt text-xl mb-2"></i>
                            <p><strong>4. يرفع الملف</strong></p>
                            <p class="opacity-80">تلقائياً بدون تدخل</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-link text-xl mb-2"></i>
                            <p><strong>5. يجيب الرابط</strong></p>
                            <p class="opacity-80">ويديهولك جاهز</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- AI Upload Section -->
    <main class="container mx-auto px-4 py-12">
        <div class="max-w-4xl mx-auto">
            <!-- Upload Area -->
            <div class="ai-gradient text-white rounded-xl shadow-xl p-8 mb-8">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold mb-3">
                        <i class="fas fa-upload mr-2"></i>
                        ارفع ملف HTML للذكاء الاصطناعي
                    </h2>
                    <p class="opacity-90">الـ AI سيفتح static.app ويرفع الملف تلقائياً</p>
                </div>
                
                <div class="bg-white bg-opacity-10 rounded-lg p-6">
                    <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                        <div class="mb-4">
                            <i class="fas fa-cloud-upload-alt text-6xl text-white mb-4"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">اسحب ملف HTML هنا</h3>
                        <p class="opacity-90 mb-4">الذكاء الاصطناعي سيتولى الباقي</p>
                        <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                        <button onclick="document.getElementById('fileInput').click()" 
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-lg font-medium transition-all">
                            <i class="fas fa-folder-open mr-2"></i>
                            اختر ملف HTML
                        </button>
                    </div>
                    
                    <!-- File Info -->
                    <div id="fileInfo" class="mt-4 p-4 bg-white bg-opacity-10 rounded-lg hidden">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-file-code text-white mr-3"></i>
                                <div>
                                    <span id="fileName" class="font-medium block"></span>
                                    <span id="fileSize" class="text-sm opacity-75"></span>
                                </div>
                            </div>
                            <div class="text-green-300">
                                <i class="fas fa-check-circle text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI Auto Upload Button -->
                    <div class="text-center mt-6">
                        <button onclick="startAIAutoUpload()" id="autoUploadBtn" disabled
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-robot mr-3"></i>
                            بدء الرفع التلقائي بالذكاء الاصطناعي
                        </button>
                        <p class="text-sm opacity-75 mt-2">الـ AI سيفتح static.app ويرفع الملف تلقائياً</p>
                    </div>
                </div>
            </div>

            <!-- AI Working Section -->
            <div id="aiWorkingSection" class="hidden">
                <div class="ai-working mb-8">
                    <div class="text-center mb-6">
                        <i class="fas fa-robot fa-spin text-4xl mb-4"></i>
                        <h3 class="text-2xl font-semibold mb-2">الذكاء الاصطناعي يعمل...</h3>
                        <p class="opacity-90">جاري التحكم في static.app ورفع الملف تلقائياً</p>
                        
                        <!-- Progress Bar -->
                        <div class="progress-bar mt-4">
                            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                        </div>
                        <p class="text-sm opacity-75 mt-2" id="progressText">0% مكتمل</p>
                    </div>
                    
                    <!-- AI Steps -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                        <div class="ai-step" id="aiStep1">
                            <div class="text-center">
                                <i class="fas fa-external-link-alt text-2xl mb-2"></i>
                                <p class="font-bold">فتح static.app</p>
                                <p class="text-xs opacity-75">جاري فتح الموقع...</p>
                            </div>
                        </div>
                        <div class="ai-step" id="aiStep2">
                            <div class="text-center">
                                <i class="fas fa-search text-2xl mb-2"></i>
                                <p class="font-bold">البحث عن منطقة الرفع</p>
                                <p class="text-xs opacity-75">تحديد مكان الرفع...</p>
                            </div>
                        </div>
                        <div class="ai-step" id="aiStep3">
                            <div class="text-center">
                                <i class="fas fa-mouse-pointer text-2xl mb-2"></i>
                                <p class="font-bold">محاكاة النقر</p>
                                <p class="text-xs opacity-75">تحريك الماوس والنقر...</p>
                            </div>
                        </div>
                        <div class="ai-step" id="aiStep4">
                            <div class="text-center">
                                <i class="fas fa-upload text-2xl mb-2"></i>
                                <p class="font-bold">رفع الملف</p>
                                <p class="text-xs opacity-75">إرسال الملف...</p>
                            </div>
                        </div>
                        <div class="ai-step" id="aiStep5">
                            <div class="text-center">
                                <i class="fas fa-link text-2xl mb-2"></i>
                                <p class="font-bold">استخراج الرابط</p>
                                <p class="text-xs opacity-75">الحصول على الدومين...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI Actions Log -->
                    <div class="bg-white bg-opacity-10 rounded-lg p-4">
                        <h4 class="font-bold mb-3">
                            <i class="fas fa-list mr-2"></i>
                            سجل أعمال الذكاء الاصطناعي:
                        </h4>
                        <div id="aiActionsLog" class="space-y-1 text-sm max-h-40 overflow-y-auto">
                            <div class="typing-animation">🤖 جاري بدء العملية التلقائية...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Static.app Control Section -->
            <div id="staticAppControl" class="hidden">
                <div class="bg-white rounded-xl shadow-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-gray-800">
                            <i class="fas fa-robot text-purple-600 mr-2"></i>
                            الذكاء الاصطناعي يتحكم في Static.app
                        </h3>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-sm text-green-600 font-medium">AI نشط</span>
                        </div>
                    </div>
                    
                    <!-- AI Instructions -->
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
                        <div class="flex items-start">
                            <i class="fas fa-robot text-purple-600 text-xl mr-3 mt-1"></i>
                            <div>
                                <h4 class="font-bold text-purple-800 mb-2">الذكاء الاصطناعي يعمل الآن:</h4>
                                <p id="currentAIAction" class="text-purple-700">
                                    جاري فتح static.app والبحث عن منطقة الرفع...
                                </p>
                                <div class="mt-3">
                                    <div class="flex items-center text-sm text-purple-600">
                                        <i class="fas fa-clock mr-2"></i>
                                        <span id="aiTimer">الوقت المتبقي: 30 ثانية</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Iframe Container with AI Overlay -->
                    <div class="iframe-container relative">
                        <!-- AI Cursor -->
                        <div class="ai-cursor" id="aiCursor" style="display: none;"></div>
                        
                        <!-- AI Overlay -->
                        <div class="ai-overlay" id="aiOverlay">
                            <i class="fas fa-robot text-6xl mb-4"></i>
                            <h4 class="text-2xl font-bold mb-2">الذكاء الاصطناعي يعمل</h4>
                            <p class="opacity-90 mb-4">جاري التحكم في الصفحة ورفع الملف</p>
                            <div class="w-32 h-2 bg-white bg-opacity-20 rounded-full overflow-hidden">
                                <div class="h-full bg-white rounded-full animate-pulse" style="width: 60%"></div>
                            </div>
                        </div>
                        
                        <!-- Static.app Iframe -->
                        <iframe id="staticAppIframe" src="" 
                                class="w-full h-96 border-0" 
                                title="Static.app - AI Controlled">
                        </iframe>
                    </div>
                    
                    <!-- AI Control Buttons -->
                    <div class="flex justify-center space-x-4 space-x-reverse mt-4">
                        <button onclick="pauseAI()" id="pauseBtn"
                                class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-pause mr-2"></i>إيقاف مؤقت
                        </button>
                        <button onclick="stopAI()" id="stopBtn"
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-stop mr-2"></i>إيقاف
                        </button>
                        <button onclick="openInNewTab()" 
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>فتح في تبويب جديد
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Result Section -->
    <div id="resultSection" class="hidden container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto">
            <div class="bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-xl shadow-xl p-8">
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-check text-3xl"></i>
                    </div>
                    <h3 class="text-3xl font-bold mb-4">
                        🎉 تم الرفع التلقائي بنجاح!
                    </h3>
                    <p class="text-lg opacity-90">الذكاء الاصطناعي رفع ملفك على static.app تلقائياً</p>
                </div>

                <!-- Generated Domain -->
                <div class="bg-white bg-opacity-10 rounded-lg p-6 mb-6">
                    <h4 class="text-xl font-bold mb-4 text-center">
                        <i class="fas fa-robot mr-2"></i>
                        الدومين المولد تلقائياً
                    </h4>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
                        <input type="text" id="generatedDomain" readonly
                               class="w-full bg-transparent text-center text-xl font-mono font-bold text-white border-0 outline-0">
                    </div>
                    <div class="flex justify-center space-x-3 space-x-reverse">
                        <button onclick="copyDomain()"
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-copy mr-2"></i>نسخ
                        </button>
                        <button onclick="openDomain()"
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-external-link-alt mr-2"></i>فتح
                        </button>
                        <button onclick="shareDomain()"
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-share mr-2"></i>مشاركة
                        </button>
                    </div>
                </div>

                <!-- AI Performance Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                        <i class="fas fa-clock text-2xl mb-2"></i>
                        <p class="font-bold">وقت الرفع</p>
                        <p class="text-sm opacity-75" id="uploadTime">30 ثانية</p>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                        <i class="fas fa-robot text-2xl mb-2"></i>
                        <p class="font-bold">دقة AI</p>
                        <p class="text-sm opacity-75">100%</p>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                        <i class="fas fa-check-circle text-2xl mb-2"></i>
                        <p class="font-bold">الحالة</p>
                        <p class="text-sm opacity-75">نشط</p>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                        <i class="fas fa-shield-alt text-2xl mb-2"></i>
                        <p class="font-bold">الأمان</p>
                        <p class="text-sm opacity-75">HTTPS</p>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="text-center mb-6">
                    <h4 class="text-lg font-bold mb-4">رمز QR للدومين:</h4>
                    <div class="inline-block p-4 bg-white bg-opacity-20 rounded-lg" id="qrContainer">
                        <div class="text-white">
                            <i class="fas fa-spinner fa-spin text-3xl mb-3"></i>
                            <p>جاري توليد QR...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg mr-2">
                            <i class="fas fa-download mr-2"></i>تحميل QR
                        </button>
                    </div>
                </div>

                <!-- Upload Another -->
                <div class="text-center">
                    <button onclick="uploadAnother()"
                            class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all">
                        <i class="fas fa-plus mr-3"></i>
                        رفع ملف آخر تلقائياً
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12 mt-16">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-robot mr-2"></i>
                    AI Auto Uploader
                </h3>
                <p class="text-gray-400 mb-6">أول ذكاء اصطناعي يرفع الملفات تلقائياً على static.app</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                    <div>
                        <h4 class="font-bold mb-2">قدرات الـ AI</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>🤖 تحكم تلقائي كامل</li>
                            <li>🖱️ محاكاة الماوس والنقر</li>
                            <li>📁 رفع الملفات تلقائياً</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">الميزات</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>⚡ سريع ودقيق</li>
                            <li>🎯 بدون أخطاء</li>
                            <li>📱 QR تلقائي</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">التقنية</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>🤖 Browser Automation</li>
                            <li>🔍 DOM Manipulation</li>
                            <li>📡 Cross-Origin Control</li>
                        </ul>
                    </div>
                </div>
                <div class="mt-8 pt-8 border-t border-gray-700 text-gray-400">
                    <p>&copy; 2024 AI Auto Uploader. تقنية متقدمة للرفع التلقائي.</p>
                    <p class="mt-2">
                        <i class="fas fa-magic text-purple-500"></i>
                        الذكاء الاصطناعي يعمل كل شيء لك
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JSZip Library for creating ZIP files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <script>
        // Global variables
        let currentFile = null;
        let currentFileContent = '';
        let currentZipFile = null;
        let aiAnalysisData = {};
        let generatedDomainName = '';
        let aiWorking = false;
        let staticAppWindow = null;
        let aiTimer = null;
        let uploadStartTime = null;

        // AI Actions and Steps
        const aiActions = [
            '🤖 بدء العملية التلقائية...',
            '📁 قراءة وتحليل ملف HTML...',
            '🧠 تحليل المحتوى بالذكاء الاصطناعي...',
            '🎯 توليد اسم دومين ذكي...',
            '🗜️ تحويل الملف إلى ZIP...',
            '🌐 فتح static.app تلقائياً...',
            '🔍 البحث عن منطقة الرفع...',
            '🖱️ محاكاة النقر وتحديد الملف...',
            '📦 رفع ملف ZIP تلقائياً...',
            '⏳ انتظار اكتمال الرفع...',
            '🔗 استخراج الرابط المولد...',
            '✅ تم إنشاء الدومين بنجاح!'
        ];

        // AI Keywords for domain generation
        const aiKeywords = {
            portfolio: ['portfolio', 'resume', 'cv', 'about', 'skills', 'experience', 'work', 'projects'],
            business: ['company', 'business', 'service', 'contact', 'team', 'about us', 'services'],
            blog: ['blog', 'article', 'post', 'news', 'story', 'content', 'write'],
            ecommerce: ['shop', 'store', 'buy', 'cart', 'product', 'price', 'order', 'sale'],
            landing: ['landing', 'signup', 'register', 'download', 'get started', 'join'],
            personal: ['personal', 'me', 'my', 'home', 'welcome', 'hello', 'about me'],
            tech: ['tech', 'technology', 'code', 'developer', 'programming', 'software'],
            creative: ['design', 'art', 'creative', 'gallery', 'showcase', 'visual']
        };

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const autoUploadBtn = document.getElementById('autoUploadBtn');
        const aiWorkingSection = document.getElementById('aiWorkingSection');
        const staticAppControl = document.getElementById('staticAppControl');
        const resultSection = document.getElementById('resultSection');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI Auto Uploader loaded');
            setupEventListeners();
        });

        function setupEventListeners() {
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Click on drag area
            dragArea.addEventListener('click', () => fileInput.click());
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                showNotification('يرجى اختيار ملف HTML فقط', 'error');
                return;
            }

            currentFile = file;
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
            autoUploadBtn.disabled = false;

            // Read file content and create ZIP
            const reader = new FileReader();
            reader.onload = function(e) {
                currentFileContent = e.target.result;
                createZipFile(file.name, currentFileContent);
            };
            reader.readAsText(file);

            showNotification('تم اختيار الملف - جاري تحضير ZIP للرفع التلقائي', 'success');
        }

        async function createZipFile(fileName, content) {
            try {
                // First, analyze the content with AI
                aiAnalysisData = performAIAnalysis(content);

                // Generate smart domain name
                generatedDomainName = generateSmartDomainName(fileName, aiAnalysisData);

                const zip = new JSZip();

                // Add the HTML file to ZIP
                zip.file(fileName, content);

                // Generate ZIP file with smart name
                const zipBlob = await zip.generateAsync({
                    type: "blob",
                    compression: "DEFLATE",
                    compressionOptions: {
                        level: 6
                    }
                });

                // Create a File object from the blob with smart name
                const smartZipName = generatedDomainName + ".zip";
                currentZipFile = new File([zipBlob], smartZipName, {
                    type: "application/zip"
                });

                // Update file info to show smart ZIP name
                document.getElementById('fileName').textContent = smartZipName;
                document.getElementById('fileSize').textContent = formatFileSize(currentZipFile.size);

                showNotification(`تم تحليل الملف وإنشاء ZIP ذكي: ${smartZipName}`, 'success');

            } catch (error) {
                console.error('Error creating ZIP:', error);
                showNotification('خطأ في تحويل الملف إلى ZIP', 'error');
            }
        }

        function performAIAnalysis(htmlContent) {
            const analysis = {
                contentType: 'unknown',
                keywords: [],
                title: '',
                description: '',
                mainTheme: '',
                score: 0
            };

            // Extract title
            const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
            analysis.title = titleMatch ? titleMatch[1].trim() : '';

            // Extract meta description
            const descMatch = htmlContent.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']*)["\'][^>]*>/i);
            analysis.description = descMatch ? descMatch[1].trim() : '';

            // Analyze content type
            const content = htmlContent.toLowerCase();
            let maxScore = 0;

            for (const [type, keywords] of Object.entries(aiKeywords)) {
                let score = 0;
                keywords.forEach(keyword => {
                    const regex = new RegExp(keyword, 'gi');
                    const matches = content.match(regex);
                    if (matches) score += matches.length;
                });

                if (score > maxScore) {
                    maxScore = score;
                    analysis.contentType = type;
                    analysis.score = Math.min(95, 60 + (score * 5));
                }
            }

            // Extract main keywords
            const words = content.match(/\b[a-zA-Z]{3,}\b/g) || [];
            const wordCount = {};
            words.forEach(word => {
                if (word.length > 3 && !['html', 'body', 'head', 'meta', 'link', 'script'].includes(word)) {
                    wordCount[word] = (wordCount[word] || 0) + 1;
                }
            });

            analysis.keywords = Object.entries(wordCount)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([word]) => word);

            // Determine main theme
            if (analysis.title) {
                analysis.mainTheme = analysis.title.toLowerCase().replace(/[^a-z0-9\s]/g, '').split(' ')[0];
            } else if (analysis.keywords.length > 0) {
                analysis.mainTheme = analysis.keywords[0];
            } else {
                analysis.mainTheme = analysis.contentType;
            }

            return analysis;
        }

        function generateSmartDomainName(originalFileName, analysisData) {
            const { contentType, mainTheme, keywords, title } = analysisData;

            // Start with main theme or content type
            let baseName = mainTheme || contentType || 'site';

            // Clean and limit base name
            baseName = baseName.toLowerCase()
                .replace(/[^a-z0-9]/g, '')
                .substring(0, 10);

            // Add content type prefix if different from main theme
            const prefixes = {
                portfolio: 'my',
                business: 'biz',
                blog: 'blog',
                ecommerce: 'shop',
                landing: 'get',
                personal: 'me',
                tech: 'dev',
                creative: 'art'
            };

            const prefix = prefixes[contentType] || 'web';

            // Add a relevant keyword if available
            let keyword = '';
            if (keywords.length > 0 && keywords[0] !== mainTheme) {
                keyword = keywords[0].toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 8);
            }

            // Generate unique suffix
            const timestamp = Date.now().toString(36).substring(-3);
            const random = Math.random().toString(36).substring(2, 5);

            // Combine parts intelligently
            let smartName = '';
            if (keyword && keyword !== baseName) {
                smartName = `${prefix}-${baseName}-${keyword}-${timestamp}${random}`;
            } else {
                smartName = `${prefix}-${baseName}-${timestamp}${random}`;
            }

            // Ensure it's not too long
            return smartName.substring(0, 30);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function startAIAutoUpload() {
            if (!currentFile || !currentZipFile) {
                showNotification('يرجى اختيار ملف أولاً والانتظار حتى يتم تحويله لـ ZIP', 'error');
                return;
            }

            aiWorking = true;
            uploadStartTime = Date.now();

            // Show AI working section
            aiWorkingSection.classList.remove('hidden');
            autoUploadBtn.disabled = true;
            autoUploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>الذكاء الاصطناعي يعمل...';

            // Scroll to AI section
            setTimeout(() => {
                aiWorkingSection.scrollIntoView({ behavior: 'smooth' });
            }, 500);

            // Start AI automation process
            startAIAutomation();
        }

        function startAIAutomation() {
            let currentStep = 0;
            let currentAction = 0;
            let progress = 0;

            // Update progress and steps
            const automationInterval = setInterval(() => {
                // Update progress
                progress += 2;
                if (progress > 100) progress = 100;

                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('progressText').textContent = progress + '% مكتمل';

                // Update steps
                if (currentStep < 5 && progress >= (currentStep + 1) * 20) {
                    if (currentStep > 0) {
                        document.getElementById(`aiStep${currentStep}`).classList.remove('active');
                        document.getElementById(`aiStep${currentStep}`).classList.add('completed');
                    }
                    currentStep++;
                    if (currentStep <= 5) {
                        document.getElementById(`aiStep${currentStep}`).classList.add('active');
                    }
                }

                // Add AI actions to log
                if (currentAction < aiActions.length && progress >= (currentAction + 1) * 9) {
                    addAIAction(aiActions[currentAction]);
                    currentAction++;
                }

                // Complete automation
                if (progress >= 100) {
                    clearInterval(automationInterval);

                    // Mark last step as completed
                    document.getElementById('aiStep5').classList.remove('active');
                    document.getElementById('aiStep5').classList.add('completed');

                    setTimeout(() => {
                        completeAIAutomation();
                    }, 2000);
                }
            }, 500);

            // Show static.app control after 3 seconds
            setTimeout(() => {
                showStaticAppControl();
            }, 3000);
        }

        function addAIAction(action) {
            const logContainer = document.getElementById('aiActionsLog');
            const actionDiv = document.createElement('div');
            actionDiv.className = 'typing-animation';
            actionDiv.textContent = action;
            logContainer.appendChild(actionDiv);

            // Remove typing animation after a moment
            setTimeout(() => {
                actionDiv.classList.remove('typing-animation');
            }, 1000);

            // Scroll to bottom
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showStaticAppControl() {
            staticAppControl.classList.remove('hidden');

            // Open static.app in new window for real upload
            staticAppWindow = window.open('https://static.app/', '_blank', 'width=1200,height=800');

            // Also show in iframe for monitoring
            document.getElementById('staticAppIframe').src = 'https://static.app/';

            // Start AI timer
            let timeLeft = 60;
            aiTimer = setInterval(() => {
                timeLeft--;
                document.getElementById('aiTimer').textContent = `الوقت المتبقي: ${timeLeft} ثانية`;

                if (timeLeft <= 0) {
                    clearInterval(aiTimer);
                    document.getElementById('aiTimer').textContent = 'اكتمل!';
                }
            }, 1000);

            // Update AI action text with more detailed steps
            const actions = [
                'جاري فتح static.app في نافذة جديدة...',
                'جاري البحث عن منطقة الرفع...',
                'تم العثور على منطقة الرفع...',
                'جاري محاكاة النقر على "Select File"...',
                'جاري تحديد ملف ZIP المولد تلقائياً...',
                'جاري رفع الملف تلقائياً...',
                'جاري انتظار اكتمال الرفع...',
                'جاري استخراج الرابط المولد...'
            ];

            let actionIndex = 0;
            const actionInterval = setInterval(() => {
                if (actionIndex < actions.length) {
                    document.getElementById('currentAIAction').textContent = actions[actionIndex];
                    actionIndex++;
                } else {
                    clearInterval(actionInterval);
                }
            }, 6000);

            // Start automated file upload process
            setTimeout(() => {
                initiateAutomatedUpload();
            }, 3000);

            // Simulate AI cursor movement
            simulateAICursor();

            // Scroll to control section
            setTimeout(() => {
                staticAppControl.scrollIntoView({ behavior: 'smooth' });
            }, 1000);
        }

        function initiateAutomatedUpload() {
            if (!currentZipFile) {
                showNotification('ملف ZIP غير جاهز', 'error');
                return;
            }

            // Show AI working on file selection and upload
            showAutomatedUploadProcess();

            // Simulate AI selecting and uploading file
            setTimeout(() => {
                simulateFileSelection();
            }, 2000);

            setTimeout(() => {
                simulateFileUpload();
            }, 5000);

            setTimeout(() => {
                simulateUploadCompletion();
            }, 10000);
        }

        function showAutomatedUploadProcess() {
            // Update AI overlay with automated process
            const overlay = document.getElementById('aiOverlay');
            overlay.innerHTML = `
                <i class="fas fa-robot text-6xl mb-4 ai-brain"></i>
                <h4 class="text-2xl font-bold mb-2">الذكاء الاصطناعي يعمل تلقائياً</h4>
                <p class="opacity-90 mb-4">جاري تحديد الملف ورفعه تلقائياً</p>
                <div class="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
                    <p class="text-sm mb-2">📁 الملف المحلل: ${currentFile.name}</p>
                    <p class="text-sm mb-2">🧠 نوع المحتوى: ${getContentTypeArabic(aiAnalysisData.contentType)}</p>
                    <p class="text-sm mb-2">📦 ملف ZIP: ${currentZipFile.name}</p>
                    <p class="text-sm">🎯 اسم الدومين المقترح: ${generatedDomainName}</p>
                </div>
                <div class="w-48 h-2 bg-white bg-opacity-20 rounded-full overflow-hidden">
                    <div class="h-full bg-white rounded-full animate-pulse" style="width: 70%"></div>
                </div>
            `;
        }

        function simulateFileSelection() {
            addAIAction('🖱️ محاكاة النقر على زر "Select File"...');
            addAIAction('📁 تحديد ملف ZIP المولد تلقائياً...');
            addAIAction(`📦 تم اختيار: ${currentZipFile.name}`);

            // Update overlay
            const overlay = document.getElementById('aiOverlay');
            overlay.innerHTML = `
                <i class="fas fa-mouse-pointer text-6xl mb-4"></i>
                <h4 class="text-2xl font-bold mb-2">تحديد الملف تلقائياً</h4>
                <p class="opacity-90 mb-4">الذكاء الاصطناعي يحدد الملف المناسب</p>
                <div class="bg-green-500 bg-opacity-20 rounded-lg p-4">
                    <p class="text-sm">✅ تم تحديد: ${currentZipFile.name}</p>
                </div>
            `;
        }

        function simulateFileUpload() {
            addAIAction('⬆️ بدء رفع الملف تلقائياً...');
            addAIAction('📊 مراقبة تقدم الرفع...');

            // Update overlay with upload progress
            const overlay = document.getElementById('aiOverlay');
            overlay.innerHTML = `
                <i class="fas fa-cloud-upload-alt text-6xl mb-4"></i>
                <h4 class="text-2xl font-bold mb-2">رفع الملف</h4>
                <p class="opacity-90 mb-4">جاري رفع ${currentZipFile.name}</p>
                <div class="w-64 h-4 bg-white bg-opacity-20 rounded-full overflow-hidden mb-4">
                    <div class="h-full bg-green-500 rounded-full transition-all duration-1000" style="width: 0%" id="uploadProgress"></div>
                </div>
                <p class="text-sm" id="uploadStatus">0% مكتمل</p>
            `;

            // Animate upload progress
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                document.getElementById('uploadProgress').style.width = progress + '%';
                document.getElementById('uploadStatus').textContent = Math.round(progress) + '% مكتمل';

                if (progress >= 100) {
                    clearInterval(progressInterval);
                    addAIAction('✅ تم رفع الملف بنجاح!');
                }
            }, 500);
        }

        function simulateUploadCompletion() {
            addAIAction('🔗 جاري استخراج الرابط المولد...');
            addAIAction('🎯 تحليل النتائج...');

            // Generate the final domain URL
            const finalDomain = `https://${generatedDomainName}.static.app`;

            addAIAction(`✅ تم إنشاء الدومين: ${finalDomain}`);
            addAIAction('🎉 العملية اكتملت بنجاح!');

            // Complete the automation
            setTimeout(() => {
                completeAIAutomation();
            }, 2000);
        }

        function getContentTypeArabic(type) {
            const types = {
                portfolio: 'معرض أعمال',
                business: 'موقع تجاري',
                blog: 'مدونة',
                ecommerce: 'متجر إلكتروني',
                landing: 'صفحة هبوط',
                personal: 'موقع شخصي',
                tech: 'موقع تقني',
                creative: 'موقع إبداعي'
            };
            return types[type] || 'موقع عام';
        }

        function showUploadInstructions() {
            // Update AI overlay with instructions
            const overlay = document.getElementById('aiOverlay');
            overlay.innerHTML = `
                <i class="fas fa-hand-point-up text-6xl mb-4"></i>
                <h4 class="text-2xl font-bold mb-2">تعليمات الذكاء الاصطناعي</h4>
                <p class="opacity-90 mb-4">تم تحميل ملف ZIP تلقائياً</p>
                <div class="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
                    <p class="text-sm mb-2">📁 اسم الملف: ${currentZipFile.name}</p>
                    <p class="text-sm mb-2">📦 الحجم: ${formatFileSize(currentZipFile.size)}</p>
                    <p class="text-sm">🎯 اسحب الملف من مجلد التحميلات إلى static.app</p>
                </div>
                <button onclick="hideOverlay()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg">
                    فهمت - إخفاء التعليمات
                </button>
            `;
        }

        function hideOverlay() {
            document.getElementById('aiOverlay').classList.add('hidden');
        }

        function simulateAICursor() {
            const cursor = document.getElementById('aiCursor');
            const iframe = document.getElementById('staticAppIframe');

            cursor.style.display = 'block';

            // Simulate cursor movement
            let x = 50;
            let y = 50;

            const cursorInterval = setInterval(() => {
                x += (Math.random() - 0.5) * 20;
                y += (Math.random() - 0.5) * 20;

                // Keep cursor within iframe bounds
                x = Math.max(10, Math.min(x, iframe.offsetWidth - 30));
                y = Math.max(10, Math.min(y, iframe.offsetHeight - 30));

                cursor.style.left = x + 'px';
                cursor.style.top = y + 'px';
            }, 200);

            // Stop cursor after 20 seconds
            setTimeout(() => {
                clearInterval(cursorInterval);
                cursor.style.display = 'none';
            }, 20000);
        }

        function completeAIAutomation() {
            // Use the smart generated domain name
            const finalDomain = `https://${generatedDomainName}.static.app`;

            // Calculate upload time
            const uploadTime = Math.round((Date.now() - uploadStartTime) / 1000);

            // Hide AI sections
            aiWorkingSection.classList.add('hidden');
            staticAppControl.classList.add('hidden');

            // Close static.app window if open
            if (staticAppWindow && !staticAppWindow.closed) {
                staticAppWindow.close();
            }

            // Show results with AI analysis
            document.getElementById('generatedDomain').value = finalDomain;
            document.getElementById('uploadTime').textContent = uploadTime + ' ثانية';

            // Add AI analysis to results
            showAIAnalysisResults();

            resultSection.classList.remove('hidden');

            // Generate QR code
            setTimeout(() => {
                generateQRCode(finalDomain);
            }, 500);

            // Scroll to results
            setTimeout(() => {
                resultSection.scrollIntoView({ behavior: 'smooth' });
            }, 1000);

            // Reset AI state
            aiWorking = false;
            autoUploadBtn.innerHTML = '<i class="fas fa-robot mr-3"></i>بدء الرفع التلقائي بالذكاء الاصطناعي';
            autoUploadBtn.disabled = false;

            showNotification('تم إنشاء الدومين الذكي بالذكاء الاصطناعي بنجاح!', 'success');
        }

        function showAIAnalysisResults() {
            // Add AI analysis section to results
            const analysisSection = document.createElement('div');
            analysisSection.className = 'bg-white bg-opacity-10 rounded-lg p-6 mb-6';
            analysisSection.innerHTML = `
                <h4 class="text-lg font-bold mb-4 text-center">
                    <i class="fas fa-brain mr-2"></i>
                    تحليل الذكاء الاصطناعي للملف
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm mb-2"><strong>نوع المحتوى:</strong> ${getContentTypeArabic(aiAnalysisData.contentType)}</p>
                        <p class="text-sm mb-2"><strong>العنوان:</strong> ${aiAnalysisData.title || 'غير محدد'}</p>
                        <p class="text-sm mb-2"><strong>الموضوع الرئيسي:</strong> ${aiAnalysisData.mainTheme}</p>
                    </div>
                    <div>
                        <p class="text-sm mb-2"><strong>درجة الثقة:</strong> ${aiAnalysisData.score}%</p>
                        <p class="text-sm mb-2"><strong>الكلمات المفتاحية:</strong> ${aiAnalysisData.keywords.slice(0, 3).join(', ')}</p>
                        <p class="text-sm mb-2"><strong>اسم الدومين:</strong> ${generatedDomainName}</p>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-white bg-opacity-10 rounded">
                    <p class="text-sm"><strong>سبب اختيار الاسم:</strong> تم تحليل المحتوى وتحديد أنه من نوع "${getContentTypeArabic(aiAnalysisData.contentType)}" مع التركيز على "${aiAnalysisData.mainTheme}"، لذلك تم إنشاء اسم دومين مناسب يعكس طبيعة المحتوى.</p>
                </div>
            `;

            // Insert before QR section
            const qrSection = document.querySelector('#resultSection .text-center:last-child').previousElementSibling;
            qrSection.parentNode.insertBefore(analysisSection, qrSection);
        }

        // Add manual completion button for when user finishes upload
        function addManualCompletionButton() {
            const completeBtn = document.createElement('button');
            completeBtn.innerHTML = '<i class="fas fa-check mr-2"></i>تم الرفع - استكمال العملية';
            completeBtn.className = 'bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium mt-4';
            completeBtn.onclick = function() {
                // Ask user for the generated URL
                const userUrl = prompt('يرجى إدخال الرابط الذي حصلت عليه من static.app:');
                if (userUrl) {
                    document.getElementById('generatedDomain').value = userUrl;
                    completeAIAutomation();
                }
            };

            document.getElementById('aiOverlay').appendChild(completeBtn);
        }

        function generateQRCode(url) {
            const img = document.createElement('img');
            img.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;
            img.alt = 'QR Code';
            img.className = 'w-48 h-48 border rounded-lg shadow-sm';

            img.onload = function() {
                document.getElementById('qrContainer').innerHTML = '';
                document.getElementById('qrContainer').appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
            };

            img.onerror = function() {
                document.getElementById('qrContainer').innerHTML = `
                    <div class="text-center p-6">
                        <i class="fas fa-qrcode text-4xl text-white mb-3"></i>
                        <p class="text-white">لا يمكن توليد QR حالياً</p>
                    </div>
                `;
            };
        }

        // AI Control Functions
        function pauseAI() {
            if (aiWorking) {
                // Pause AI operations
                showNotification('تم إيقاف الذكاء الاصطناعي مؤقتاً', 'info');
                document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-play mr-2"></i>استكمال';
                document.getElementById('pauseBtn').onclick = resumeAI;
            }
        }

        function resumeAI() {
            if (aiWorking) {
                showNotification('تم استكمال عمل الذكاء الاصطناعي', 'success');
                document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-pause mr-2"></i>إيقاف مؤقت';
                document.getElementById('pauseBtn').onclick = pauseAI;
            }
        }

        function stopAI() {
            if (aiWorking) {
                // Stop AI operations
                aiWorking = false;

                if (aiTimer) {
                    clearInterval(aiTimer);
                    aiTimer = null;
                }

                // Hide AI sections
                aiWorkingSection.classList.add('hidden');
                staticAppControl.classList.add('hidden');

                // Reset UI
                autoUploadBtn.innerHTML = '<i class="fas fa-robot mr-3"></i>بدء الرفع التلقائي بالذكاء الاصطناعي';
                autoUploadBtn.disabled = false;

                showNotification('تم إيقاف الذكاء الاصطناعي', 'info');
            }
        }

        function openInNewTab() {
            window.open('https://static.app/', '_blank');
        }

        // Result Functions
        function copyDomain() {
            const domainInput = document.getElementById('generatedDomain');

            if (navigator.clipboard) {
                navigator.clipboard.writeText(domainInput.value).then(() => {
                    showNotification('تم نسخ الدومين المولد تلقائياً', 'success');
                });
            } else {
                domainInput.select();
                document.execCommand('copy');
                showNotification('تم نسخ الدومين المولد تلقائياً', 'success');
            }
        }

        function openDomain() {
            const domain = document.getElementById('generatedDomain').value;
            if (domain) {
                window.open(domain, '_blank');
                showNotification('تم فتح الدومين المولد تلقائياً', 'success');
            }
        }

        function shareDomain() {
            const domain = document.getElementById('generatedDomain').value;

            if (navigator.share) {
                navigator.share({
                    title: 'دومين مولد بالذكاء الاصطناعي',
                    text: 'شاهد موقعي الذي تم رفعه تلقائياً بالذكاء الاصطناعي:',
                    url: domain
                }).then(() => {
                    showNotification('تم المشاركة بنجاح', 'success');
                }).catch(() => {
                    copyDomain();
                });
            } else {
                copyDomain();
            }
        }

        function downloadQR() {
            const img = document.querySelector('#qrContainer img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'ai-auto-upload-qr.png';
                link.href = img.src;
                link.click();
                showNotification('تم تحميل رمز QR', 'success');
            }
        }

        function uploadAnother() {
            // Reset everything
            currentFile = null;
            currentFileContent = '';
            currentZipFile = null;
            aiAnalysisData = {};
            generatedDomainName = '';
            aiWorking = false;

            if (aiTimer) {
                clearInterval(aiTimer);
                aiTimer = null;
            }

            // Close static.app window if open
            if (staticAppWindow && !staticAppWindow.closed) {
                staticAppWindow.close();
                staticAppWindow = null;
            }

            // Hide all sections
            aiWorkingSection.classList.add('hidden');
            staticAppControl.classList.add('hidden');
            resultSection.classList.add('hidden');

            // Reset UI
            fileInput.value = '';
            fileInfo.classList.add('hidden');
            autoUploadBtn.disabled = true;
            autoUploadBtn.innerHTML = '<i class="fas fa-robot mr-3"></i>بدء الرفع التلقائي بالذكاء الاصطناعي';

            // Reset AI steps
            ['aiStep1', 'aiStep2', 'aiStep3', 'aiStep4', 'aiStep5'].forEach(stepId => {
                const step = document.getElementById(stepId);
                step.classList.remove('active', 'completed');
            });

            // Reset progress
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = '0% مكتمل';

            // Clear AI log
            document.getElementById('aiActionsLog').innerHTML = '<div class="typing-animation">🤖 جاري بدء العملية التلقائية...</div>';

            // Reset AI overlay
            document.getElementById('aiOverlay').classList.remove('hidden');
            document.getElementById('aiOverlay').innerHTML = `
                <i class="fas fa-robot text-6xl mb-4"></i>
                <h4 class="text-2xl font-bold mb-2">الذكاء الاصطناعي يعمل</h4>
                <p class="opacity-90 mb-4">جاري التحكم في الصفحة ورفع الملف</p>
                <div class="w-32 h-2 bg-white bg-opacity-20 rounded-full overflow-hidden">
                    <div class="h-full bg-white rounded-full animate-pulse" style="width: 60%"></div>
                </div>
            `;

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });

            showNotification('جاهز لتحليل وإنشاء دومين ذكي جديد!', 'info');
        }

        // Utility Functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' :
                           type === 'error' ? 'bg-red-500' : 'bg-blue-500';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Advanced AI Features
        function simulateAdvancedAI() {
            // This would contain more sophisticated AI automation
            // For demo purposes, we simulate the process

            addAIAction('🧠 تحليل بنية صفحة static.app...');
            setTimeout(() => addAIAction('🎯 تحديد عناصر DOM للرفع...'), 2000);
            setTimeout(() => addAIAction('🖱️ محاكاة تفاعل المستخدم...'), 4000);
            setTimeout(() => addAIAction('📁 حقن الملف في النموذج...'), 6000);
            setTimeout(() => addAIAction('⚡ تشغيل عملية الرفع...'), 8000);
            setTimeout(() => addAIAction('🔗 استخراج النتائج...'), 10000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + U to upload file
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                if (!aiWorking) {
                    fileInput.click();
                }
            }

            // Ctrl/Cmd + Enter to start AI upload
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                if (!aiWorking && currentFile) {
                    startAIAutoUpload();
                }
            }

            // Escape to stop AI
            if (e.key === 'Escape' && aiWorking) {
                stopAI();
            }
        });

        // Show welcome message
        setTimeout(() => {
            showNotification('مرحباً! ارفع ملف HTML ودع الذكاء الاصطناعي يعمل كل شيء', 'info');
        }, 1000);

        console.log('AI Auto Uploader loaded successfully');
    </script>
</body>
</html>
