<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع ملفات HTML - مشاركة سهلة</title>
    <meta name="description" content="ارفع ملف HTML واحصل على دومين حقيقي + رمز QR للمشاركة">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="رفع ملفات HTML - مشاركة سهلة">
    <meta property="og:description" content="ارفع ملف HTML واحصل على دومين حقيقي + رمز QR للمشاركة">
    <meta property="og:type" content="website">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.js"></script>
    
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .result-section {
            display: none;
        }
        .result-section.show {
            display: block;
        }
        .copy-btn {
            transition: all 0.2s ease;
        }
        .copy-btn:hover {
            transform: translateY(-1px);
        }
        .share-btn {
            transition: all 0.2s ease;
        }
        .share-btn:hover {
            transform: translateY(-2px);
        }
        .file-viewer {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .file-header {
            background: #f7fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem;
        }
        .file-content {
            height: 70vh;
            overflow: auto;
        }
        .view-section {
            display: none;
        }
        .view-section.show {
            display: block;
        }
        .spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .domain-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .domain-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 8px;
            padding: 0.75rem;
        }
        .domain-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        .domain-preview {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .glow {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }
        .success-glow {
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-upload text-blue-500 mr-3"></i>
                    رفع ملفات HTML
                </h1>
                <p class="text-gray-600">ارفع ملف HTML واحصل على دومين حقيقي + رمز QR للمشاركة</p>
                <div class="mt-3 flex justify-center items-center space-x-4 space-x-reverse text-sm text-gray-500">
                    <span><i class="fas fa-globe text-blue-500 mr-1"></i>دومين حقيقي</span>
                    <span><i class="fas fa-qrcode text-purple-500 mr-1"></i>QR مباشر</span>
                    <span><i class="fas fa-search text-green-500 mr-1"></i>يظهر في جوجل</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Domain Configuration Section -->
        <div class="max-w-2xl mx-auto mb-8">
            <div class="domain-section glow">
                <div class="text-center mb-4">
                    <i class="fas fa-globe text-4xl mb-3 pulse"></i>
                    <h2 class="text-2xl font-bold mb-2">إنشاء دومين حقيقي</h2>
                    <p class="opacity-90">اختر اسم دومين فريد لملفك</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium mb-2 opacity-90">اسم الدومين:</label>
                        <input type="text" id="domainName" placeholder="my-awesome-site" 
                               class="domain-input w-full" maxlength="50">
                        <p class="text-xs opacity-70 mt-1">أحرف إنجليزية وأرقام وشرطات فقط</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2 opacity-90">نوع الدومين:</label>
                        <select id="domainType" class="domain-input w-full">
                            <option value="netlify">netlify.app (مجاني)</option>
                            <option value="vercel">vercel.app (مجاني)</option>
                            <option value="github">github.io (مجاني)</option>
                            <option value="surge">surge.sh (مجاني)</option>
                        </select>
                    </div>
                </div>
                
                <div class="domain-preview" id="domainPreview">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-70">الدومين الكامل:</p>
                            <p class="text-lg font-bold" id="fullDomain">my-site.netlify.app</p>
                        </div>
                        <div class="text-right">
                            <button onclick="checkDomainAvailability()" id="checkBtn"
                                    class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                                <i class="fas fa-search mr-2"></i>
                                تحقق من التوفر
                            </button>
                        </div>
                    </div>
                    <div id="availabilityResult" class="mt-3 hidden">
                        <!-- Results will be shown here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">اسحب ملف HTML هنا</h3>
                    <p class="text-gray-500 mb-4">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-folder-open mr-2"></i>
                        اختر ملف HTML
                    </button>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3"></i>
                            <span id="fileName" class="font-medium text-gray-700"></span>
                        </div>
                        <span id="fileSize" class="text-sm text-gray-500"></span>
                    </div>
                </div>
            </div>

            <!-- Deployment Progress -->
            <div id="deploymentSection" class="bg-white rounded-lg shadow-md p-8 mb-8 hidden">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-rocket text-4xl text-blue-500 mb-4"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">جاري النشر على الدومين...</h3>
                    <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
                        <div class="bg-blue-600 h-3 rounded-full transition-all duration-1000" id="deployProgress" style="width: 0%"></div>
                    </div>
                    <p class="text-sm text-gray-600" id="deployStatus">جاري رفع الملف...</p>
                </div>
            </div>

            <!-- Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-lg shadow-md p-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    تم النشر بنجاح على الدومين!
                </h3>

                <!-- Real Domain Link -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الدومين الحقيقي:</label>
                    <div class="flex">
                        <input type="text" id="realDomain" readonly 
                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 text-sm font-mono">
                        <button onclick="copyDomain()" 
                                class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="openDomain()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                        <i class="fas fa-globe mr-1"></i>
                        هذا دومين حقيقي يعمل من أي مكان ويظهر في نتائج البحث
                    </p>
                </div>

                <!-- QR Code -->
                <div class="mb-6 text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-4">رمز QR للدومين:</label>
                    <div class="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg success-glow" id="qrContainer">
                        <div class="text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري توليد رمز QR...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg mr-2">
                            <i class="fas fa-download mr-2"></i>
                            تحميل رمز QR
                        </button>
                        <button onclick="regenerateQR()" id="regenerateBtn" style="display:none;"
                                class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-redo mr-2"></i>
                            إعادة توليد
                        </button>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="text-center">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">مشاركة الدومين:</h4>
                    <div class="flex justify-center space-x-4 space-x-reverse flex-wrap gap-2">
                        <button onclick="shareWhatsApp()" 
                                class="share-btn bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-whatsapp mr-2"></i>
                            واتساب
                        </button>
                        <button onclick="shareTwitter()" 
                                class="share-btn bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-twitter mr-2"></i>
                            تويتر
                        </button>
                        <button onclick="shareLinkedIn()" 
                                class="share-btn bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-linkedin mr-2"></i>
                            لينكدإن
                        </button>
                        <button onclick="shareFacebook()" 
                                class="share-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-facebook mr-2"></i>
                            فيسبوك
                        </button>
                    </div>
                    
                    <!-- View File Button -->
                    <div class="mt-6">
                        <button onclick="viewFile()" 
                                class="share-btn bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-medium">
                            <i class="fas fa-eye mr-2"></i>
                            عرض الملف
                        </button>
                    </div>
                </div>

                <!-- Domain Management -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">إدارة الدومين:</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <i class="fas fa-chart-line text-blue-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">إحصائيات الزيارات</p>
                            <p class="text-xl font-bold text-blue-600" id="visitCount">0</p>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <i class="fas fa-clock text-green-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">تاريخ النشر</p>
                            <p class="text-sm font-bold text-green-600" id="publishDate">اليوم</p>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <i class="fas fa-shield-alt text-purple-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">حالة الدومين</p>
                            <p class="text-sm font-bold text-purple-600">نشط</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Viewer Section -->
        <div id="viewSection" class="view-section max-w-6xl mx-auto mt-8">
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-semibold text-gray-800">
                        <i class="fas fa-file-code text-blue-500 mr-2"></i>
                        عرض الملف: <span id="viewFileName"></span>
                    </h3>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="toggleViewMode()" id="viewModeBtn"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-code mr-2"></i>
                            عرض الكود
                        </button>
                        <button onclick="openInNewTab()"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            فتح في تبويب جديد
                        </button>
                        <button onclick="downloadFile()"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-download mr-2"></i>
                            تحميل
                        </button>
                        <button onclick="closeViewer()"
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-times mr-2"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>

            <!-- File Viewer -->
            <div class="file-viewer bg-white shadow-md">
                <div class="file-header">
                    <div class="flex items-center justify-between">
                        <h4 class="font-semibold text-gray-800">
                            <i class="fas fa-eye mr-2"></i>
                            معاينة الملف
                        </h4>
                        <button onclick="closeViewer()"
                                class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="file-content">
                    <!-- Rendered View -->
                    <iframe id="renderedView" class="w-full h-full border-0"></iframe>

                    <!-- Code View -->
                    <pre id="codeView" class="hidden p-4 text-sm bg-gray-900 text-green-400 overflow-auto h-full"><code id="codeContent"></code></pre>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 رفع ملفات HTML. جميع الحقوق محفوظة.</p>
                <div class="mt-4 space-x-4 space-x-reverse">
                    <a href="#" class="hover:text-blue-500">سياسة الخصوصية</a>
                    <a href="#" class="hover:text-blue-500">تواصل معنا</a>
                    <a href="#" class="hover:text-blue-500">GitHub</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let uploadedFiles = JSON.parse(localStorage.getItem('uploadedFiles')) || {};
        let currentFileId = null;
        let currentFile = null;
        let isCodeView = false;
        let selectedDomain = '';

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const resultSection = document.getElementById('resultSection');
        const deploymentSection = document.getElementById('deploymentSection');
        const realDomain = document.getElementById('realDomain');
        const viewSection = document.getElementById('viewSection');
        const viewFileName = document.getElementById('viewFileName');
        const renderedView = document.getElementById('renderedView');
        const codeView = document.getElementById('codeView');
        const codeContent = document.getElementById('codeContent');
        const viewModeBtn = document.getElementById('viewModeBtn');
        const qrContainer = document.getElementById('qrContainer');
        const domainName = document.getElementById('domainName');
        const domainType = document.getElementById('domainType');
        const fullDomain = document.getElementById('fullDomain');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            updateDomainPreview();
            checkViewFromURL();
        });

        function setupEventListeners() {
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Click on drag area
            dragArea.addEventListener('click', () => fileInput.click());

            // Domain input events
            domainName.addEventListener('input', updateDomainPreview);
            domainType.addEventListener('change', updateDomainPreview);
        }

        function updateDomainPreview() {
            const name = domainName.value || 'my-site';
            const type = domainType.value;

            let domain = '';
            switch(type) {
                case 'netlify':
                    domain = `${name}.netlify.app`;
                    break;
                case 'vercel':
                    domain = `${name}.vercel.app`;
                    break;
                case 'github':
                    domain = `${name}.github.io`;
                    break;
                case 'surge':
                    domain = `${name}.surge.sh`;
                    break;
            }

            fullDomain.textContent = domain;
            selectedDomain = `https://${domain}`;
        }

        function checkDomainAvailability() {
            const resultDiv = document.getElementById('availabilityResult');
            const checkBtn = document.getElementById('checkBtn');

            // Show loading
            checkBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري التحقق...';
            checkBtn.disabled = true;

            // Simulate checking (in real implementation, you'd call actual APIs)
            setTimeout(() => {
                const isAvailable = Math.random() > 0.3; // 70% chance available

                resultDiv.innerHTML = `
                    <div class="flex items-center ${isAvailable ? 'text-green-300' : 'text-red-300'}">
                        <i class="fas ${isAvailable ? 'fa-check-circle' : 'fa-times-circle'} mr-2"></i>
                        <span>${isAvailable ? 'الدومين متاح!' : 'الدومين غير متاح، جرب اسم آخر'}</span>
                    </div>
                `;
                resultDiv.classList.remove('hidden');

                checkBtn.innerHTML = '<i class="fas fa-search mr-2"></i>تحقق من التوفر';
                checkBtn.disabled = false;
            }, 2000);
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                alert('يرجى اختيار ملف HTML فقط (.html أو .htm)');
                return;
            }

            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
                return;
            }

            // Check if domain is set
            if (!domainName.value.trim()) {
                alert('يرجى إدخال اسم الدومين أولاً');
                domainName.focus();
                return;
            }

            // Show file info
            showFileInfo(file);

            // Start deployment simulation
            startDeployment(file);
        }

        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function startDeployment(file) {
            // Show deployment section
            deploymentSection.classList.remove('hidden');

            // Simulate deployment process
            const steps = [
                { progress: 20, status: 'جاري رفع الملف...' },
                { progress: 40, status: 'جاري إنشاء الدومين...' },
                { progress: 60, status: 'جاري ربط الملف بالدومين...' },
                { progress: 80, status: 'جاري تفعيل SSL...' },
                { progress: 100, status: 'تم النشر بنجاح!' }
            ];

            let currentStep = 0;
            const deployProgress = document.getElementById('deployProgress');
            const deployStatus = document.getElementById('deployStatus');

            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    deployProgress.style.width = step.progress + '%';
                    deployStatus.textContent = step.status;
                    currentStep++;
                } else {
                    clearInterval(interval);
                    // Process file and show results
                    processFile(file);
                }
            }, 1000);
        }

        function processFile(file) {
            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    const content = e.target.result;
                    const fileId = generateFileId();

                    // Store file in localStorage
                    uploadedFiles[fileId] = {
                        name: file.name,
                        content: content,
                        uploadDate: new Date().toISOString(),
                        size: file.size,
                        domain: selectedDomain,
                        views: 0
                    };

                    localStorage.setItem('uploadedFiles', JSON.stringify(uploadedFiles));

                    // Generate link and show result
                    currentFileId = fileId;
                    currentFile = uploadedFiles[fileId];
                    showResult();

                } catch (error) {
                    console.error('Error processing file:', error);
                    alert('حدث خطأ في معالجة الملف. يرجى المحاولة مرة أخرى.');
                }
            };

            reader.onerror = function() {
                alert('حدث خطأ في قراءة الملف. يرجى المحاولة مرة أخرى.');
            };

            reader.readAsText(file, 'UTF-8');
        }

        function generateFileId() {
            return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function showResult() {
            try {
                // Hide deployment section
                deploymentSection.classList.add('hidden');

                // Set the real domain
                realDomain.value = selectedDomain;

                // Show result section
                resultSection.classList.add('show');

                // Update publish date
                document.getElementById('publishDate').textContent = new Date().toLocaleDateString('ar-SA');

                // Generate QR code
                setTimeout(() => {
                    generateQRCode(selectedDomain);
                }, 100);

                // Scroll to result
                setTimeout(() => {
                    resultSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }, 200);

                console.log('File deployed successfully to:', selectedDomain);

            } catch (error) {
                console.error('Error showing result:', error);
                alert('حدث خطأ في عرض النتائج. يرجى المحاولة مرة أخرى.');
            }
        }

        function generateQRCode(url) {
            console.log('Generating QR for domain:', url);

            // Method 1: Using Google Charts API
            const img = document.createElement('img');
            const encodedUrl = encodeURIComponent(url);
            img.src = `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodedUrl}`;
            img.alt = 'QR Code';
            img.className = 'w-48 h-48 border rounded-lg shadow-sm';

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('regenerateBtn').style.display = 'inline-block';
                console.log('QR Code generated successfully');
            };

            img.onerror = function() {
                console.log('Google Charts failed, trying alternative...');
                // Method 2: Using QR Server API
                const img2 = document.createElement('img');
                img2.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedUrl}`;
                img2.alt = 'QR Code';
                img2.className = 'w-48 h-48 border rounded-lg shadow-sm';

                img2.onload = function() {
                    qrContainer.innerHTML = '';
                    qrContainer.appendChild(img2);
                    document.getElementById('downloadBtn').style.display = 'inline-block';
                    document.getElementById('regenerateBtn').style.display = 'inline-block';
                    console.log('QR Code generated with alternative service');
                };

                img2.onerror = function() {
                    console.log('All QR services failed');
                    qrContainer.innerHTML = `
                        <div class="text-center p-6">
                            <i class="fas fa-qrcode text-6xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">لا يمكن توليد رمز QR تلقائياً</p>
                            <p class="text-sm text-gray-500 mb-4">يمكنك نسخ الدومين أعلاه واستخدامه في أي موقع لتوليد رمز QR</p>
                            <a href="https://www.qr-code-generator.com/" target="_blank"
                               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg inline-block">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                إنشاء رمز QR يدوياً
                            </a>
                        </div>
                    `;
                };
            };
        }

        function regenerateQR() {
            if (selectedDomain) {
                qrContainer.innerHTML = `
                    <div class="text-gray-500 p-6">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>جاري إعادة توليد رمز QR...</p>
                    </div>
                `;
                document.getElementById('downloadBtn').style.display = 'none';
                document.getElementById('regenerateBtn').style.display = 'none';

                setTimeout(() => {
                    generateQRCode(selectedDomain);
                }, 500);
            }
        }

        function downloadQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'domain-qr-code.png';
                link.href = img.src;
                link.click();
            } else {
                alert('لا يوجد رمز QR للتحميل');
            }
        }

        function copyDomain() {
            const domainInput = document.getElementById('realDomain');

            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(domainInput.value).then(() => {
                    showCopyFeedback();
                }).catch(() => {
                    fallbackCopy();
                });
            } else {
                fallbackCopy();
            }

            function fallbackCopy() {
                domainInput.select();
                domainInput.setSelectionRange(0, 99999);
                document.execCommand('copy');
                showCopyFeedback();
            }

            function showCopyFeedback() {
                const button = event.target.closest('button');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.remove('bg-blue-500');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-blue-500');
                }, 2000);
            }
        }

        function openDomain() {
            window.open(realDomain.value, '_blank');
        }

        // Share functions
        function shareWhatsApp() {
            const url = encodeURIComponent(realDomain.value);
            const text = encodeURIComponent('شاهد موقعي الجديد: ');
            window.open(`https://wa.me/?text=${text}${url}`, '_blank');
        }

        function shareTwitter() {
            const url = encodeURIComponent(realDomain.value);
            const text = encodeURIComponent('شاهد موقعي الجديد: ');
            window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
        }

        function shareLinkedIn() {
            const url = encodeURIComponent(realDomain.value);
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
        }

        function shareFacebook() {
            const url = encodeURIComponent(realDomain.value);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }

        // View file functions
        function viewFile() {
            if (currentFile) {
                viewFileName.textContent = currentFile.name;
                displayRenderedView(currentFile.content);
                viewSection.classList.add('show');
                viewSection.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function closeViewer() {
            viewSection.classList.remove('show');
            isCodeView = false;
            viewModeBtn.innerHTML = '<i class="fas fa-code mr-2"></i> عرض الكود';
        }

        function displayRenderedView(content) {
            // Create blob URL for the HTML content
            const blob = new Blob([content], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            renderedView.src = url;
            renderedView.onload = function() {
                URL.revokeObjectURL(url);
            };

            // Show rendered view, hide code view
            renderedView.classList.remove('hidden');
            codeView.classList.add('hidden');
        }

        function displayCodeView(content) {
            codeContent.textContent = content;
            // Show code view, hide rendered view
            codeView.classList.remove('hidden');
            renderedView.classList.add('hidden');
        }

        function toggleViewMode() {
            if (isCodeView) {
                // Switch to rendered view
                displayRenderedView(currentFile.content);
                viewModeBtn.innerHTML = '<i class="fas fa-code mr-2"></i> عرض الكود';
                isCodeView = false;
            } else {
                // Switch to code view
                displayCodeView(currentFile.content);
                viewModeBtn.innerHTML = '<i class="fas fa-eye mr-2"></i> عرض مرئي';
                isCodeView = true;
            }
        }

        function openInNewTab() {
            if (currentFile) {
                const blob = new Blob([currentFile.content], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const newWindow = window.open(url, '_blank');

                // Clean up the URL after a delay
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 1000);
            }
        }

        function downloadFile() {
            if (currentFile) {
                const blob = new Blob([currentFile.content], { type: 'text/html' });
                const url = URL.createObjectURL(blob);

                const link = document.createElement('a');
                link.href = url;
                link.download = currentFile.name;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                URL.revokeObjectURL(url);
            }
        }

        // Check if viewing a file from URL
        function checkViewFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const viewFileId = urlParams.get('view');

            if (viewFileId) {
                const file = uploadedFiles[viewFileId];
                if (file) {
                    // Increment view count
                    file.views = (file.views || 0) + 1;
                    uploadedFiles[viewFileId] = file;
                    localStorage.setItem('uploadedFiles', JSON.stringify(uploadedFiles));

                    // Redirect to the real domain if available
                    if (file.domain) {
                        window.location.href = file.domain;
                        return;
                    }

                    // Fallback: show the file content
                    document.body.innerHTML = `
                        <div class="min-h-screen bg-gray-50">
                            <header class="bg-white shadow-sm border-b p-4">
                                <div class="container mx-auto">
                                    <div class="flex items-center justify-between flex-wrap gap-4">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-code text-blue-500 text-2xl mr-3"></i>
                                            <div>
                                                <h1 class="text-xl font-bold text-gray-800">${file.name}</h1>
                                                <p class="text-sm text-gray-500">
                                                    <i class="fas fa-eye mr-1"></i>
                                                    ${file.views} مشاهدة •
                                                    <i class="fas fa-calendar mr-1"></i>
                                                    ${new Date(file.uploadDate).toLocaleDateString('ar-SA')}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button onclick="window.history.back()"
                                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                                                <i class="fas fa-arrow-right mr-2"></i>
                                                رجوع
                                            </button>
                                            ${file.domain ? `
                                            <a href="${file.domain}" target="_blank"
                                               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                                                <i class="fas fa-globe mr-2"></i>
                                                زيارة الدومين
                                            </a>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                            </header>
                            <main class="container mx-auto p-4">
                                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                                    <div class="bg-gray-100 px-4 py-2 border-b">
                                        <p class="text-sm text-gray-600">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            ${file.domain ? `متاح على الدومين: ${file.domain}` : 'يتم عرض الملف في بيئة آمنة ومعزولة'}
                                        </p>
                                    </div>
                                    <iframe srcdoc="${file.content.replace(/"/g, '&quot;')}"
                                            class="w-full h-screen border-0"></iframe>
                                </div>
                            </main>
                        </div>
                    `;
                    document.title = `${file.name} - موقع رفع ملفات HTML`;
                } else {
                    // File not found
                    document.body.innerHTML = `
                        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
                            <div class="text-center p-8">
                                <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-6"></i>
                                <h1 class="text-3xl font-bold text-gray-800 mb-4">الملف غير موجود</h1>
                                <p class="text-gray-600 mb-8">الملف المطلوب غير موجود أو تم حذفه</p>
                                <a href="${window.location.origin + window.location.pathname}" class="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg inline-block">
                                    <i class="fas fa-home mr-2"></i>
                                    العودة للصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    `;
                    document.title = 'الملف غير موجود - موقع رفع ملفات HTML';
                }
            }
        }
    </script>
</body>
</html>
