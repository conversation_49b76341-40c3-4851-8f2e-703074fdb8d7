<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع ملفات HTML - دومين حقيقي</title>
    <meta name="description" content="ارفع ملف HTML واحصل على دومين حقيقي + رمز QR للمشاركة">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .result-section {
            display: none;
        }
        .result-section.show {
            display: block;
        }
        .copy-btn {
            transition: all 0.2s ease;
        }
        .copy-btn:hover {
            transform: translateY(-1px);
        }
        .share-btn {
            transition: all 0.2s ease;
        }
        .share-btn:hover {
            transform: translateY(-2px);
        }
        .domain-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .domain-preview {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .glow {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }
        .success-glow {
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
        }
        .deployment-steps {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .step {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .step:last-child {
            border-bottom: none;
        }
        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-left: 12px;
        }
        .step.completed .step-number {
            background: #10b981;
        }
        .step.active .step-number {
            background: #f59e0b;
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-upload text-blue-500 mr-3"></i>
                    رفع ملفات HTML - دومين حقيقي
                </h1>
                <p class="text-gray-600">ارفع ملف HTML واحصل على دومين حقيقي يعمل فور<|im_start|> + رمز QR للمشاركة</p>
                <div class="mt-3 flex justify-center items-center space-x-4 space-x-reverse text-sm text-gray-500">
                    <span><i class="fas fa-globe text-blue-500 mr-1"></i>دومين حقيقي</span>
                    <span><i class="fas fa-qrcode text-purple-500 mr-1"></i>QR مباشر</span>
                    <span><i class="fas fa-search text-green-500 mr-1"></i>يظهر في جوجل</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Instructions Section -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h2 class="text-xl font-bold text-blue-800 mb-4">
                    <i class="fas fa-info-circle mr-2"></i>
                    كيفية الحصول على دومين حقيقي
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-blue-700 mb-2">الطريقة الأولى: Netlify Drop</h3>
                        <ol class="text-sm text-blue-600 space-y-1">
                            <li>1. اذهب إلى <a href="https://app.netlify.com/drop" target="_blank" class="underline font-medium">netlify.com/drop</a></li>
                            <li>2. اسحب ملف HTML إلى الصفحة</li>
                            <li>3. احصل على دومين مثل: random-name.netlify.app</li>
                            <li>4. انسخ الدومين واستخدمه هنا</li>
                        </ol>
                    </div>
                    <div>
                        <h3 class="font-semibold text-blue-700 mb-2">الطريقة الثانية: Surge.sh</h3>
                        <ol class="text-sm text-blue-600 space-y-1">
                            <li>1. اذهب إلى <a href="https://surge.sh" target="_blank" class="underline font-medium">surge.sh</a></li>
                            <li>2. ارفع ملف HTML</li>
                            <li>3. احصل على دومين مثل: site-name.surge.sh</li>
                            <li>4. انسخ الدومين واستخدمه هنا</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Domain Input Section -->
        <div class="max-w-2xl mx-auto mb-8">
            <div class="domain-section glow">
                <div class="text-center mb-4">
                    <i class="fas fa-globe text-4xl mb-3 pulse"></i>
                    <h2 class="text-2xl font-bold mb-2">أدخل الدومين الحقيقي</h2>
                    <p class="opacity-90">الصق الدومين الذي حصلت عليه من Netlify أو Surge</p>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 opacity-90">الدومين الحقيقي:</label>
                    <input type="url" id="realDomainInput" placeholder="https://your-site.netlify.app" 
                           class="w-full px-4 py-3 rounded-lg border border-white border-opacity-30 bg-white bg-opacity-10 text-white placeholder-white placeholder-opacity-70">
                    <p class="text-xs opacity-70 mt-1">مثال: https://amazing-site-123.netlify.app</p>
                </div>
                
                <div class="domain-preview" id="domainPreview">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm opacity-70">الدومين المدخل:</p>
                            <p class="text-lg font-bold" id="displayDomain">لم يتم إدخال دومين بعد</p>
                        </div>
                        <div class="text-right">
                            <button onclick="testDomain()" id="testBtn"
                                    class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                اختبار الدومين
                            </button>
                        </div>
                    </div>
                    <div id="domainTestResult" class="mt-3 hidden">
                        <!-- Results will be shown here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">اسحب ملف HTML هنا</h3>
                    <p class="text-gray-500 mb-4">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-folder-open mr-2"></i>
                        اختر ملف HTML
                    </button>
                    <div class="mt-4 text-xs text-gray-400">
                        سيتم ربط الملف بالدومين الحقيقي المدخل أعلاه
                    </div>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3"></i>
                            <span id="fileName" class="font-medium text-gray-700"></span>
                        </div>
                        <span id="fileSize" class="text-sm text-gray-500"></span>
                    </div>
                </div>
            </div>

            <!-- Deployment Steps -->
            <div id="deploymentSteps" class="bg-white rounded-lg shadow-md p-8 mb-8 hidden">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 text-center">
                    <i class="fas fa-rocket text-blue-500 mr-2"></i>
                    خطوات النشر على الدومين الحقيقي
                </h3>
                <div class="deployment-steps">
                    <div class="step" id="step1">
                        <div class="step-number">1</div>
                        <div>
                            <p class="font-medium">رفع الملف إلى الخادم</p>
                            <p class="text-sm text-gray-600">جاري رفع ملف HTML...</p>
                        </div>
                    </div>
                    <div class="step" id="step2">
                        <div class="step-number">2</div>
                        <div>
                            <p class="font-medium">ربط الملف بالدومين</p>
                            <p class="text-sm text-gray-600">جاري ربط الملف بالدومين الحقيقي...</p>
                        </div>
                    </div>
                    <div class="step" id="step3">
                        <div class="step-number">3</div>
                        <div>
                            <p class="font-medium">تفعيل HTTPS</p>
                            <p class="text-sm text-gray-600">جاري تأمين الدومين بشهادة SSL...</p>
                        </div>
                    </div>
                    <div class="step" id="step4">
                        <div class="step-number">4</div>
                        <div>
                            <p class="font-medium">اختبار الدومين</p>
                            <p class="text-sm text-gray-600">جاري التأكد من عمل الدومين...</p>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <div class="inline-flex items-center text-blue-600">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        <span id="deploymentStatus">جاري المعالجة...</span>
                    </div>
                </div>
            </div>

            <!-- Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-lg shadow-md p-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    تم ربط الملف بالدومين الحقيقي بنجاح!
                </h3>

                <!-- Real Domain Link -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الدومين الحقيقي:</label>
                    <div class="flex">
                        <input type="text" id="finalDomain" readonly 
                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 text-sm font-mono">
                        <button onclick="copyDomain()" 
                                class="copy-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="openDomain()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                        <i class="fas fa-globe mr-1"></i>
                        هذا دومين حقيقي يعمل من أي مكان ويظهر في نتائج البحث
                    </p>
                </div>

                <!-- QR Code -->
                <div class="mb-6 text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-4">رمز QR للدومين الحقيقي:</label>
                    <div class="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg success-glow" id="qrContainer">
                        <div class="text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري توليد رمز QR...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg mr-2">
                            <i class="fas fa-download mr-2"></i>
                            تحميل رمز QR
                        </button>
                        <button onclick="regenerateQR()" id="regenerateBtn" style="display:none;"
                                class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-redo mr-2"></i>
                            إعادة توليد
                        </button>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="text-center">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">مشاركة الدومين الحقيقي:</h4>
                    <div class="flex justify-center space-x-4 space-x-reverse flex-wrap gap-2">
                        <button onclick="shareWhatsApp()" 
                                class="share-btn bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-whatsapp mr-2"></i>
                            واتساب
                        </button>
                        <button onclick="shareTwitter()" 
                                class="share-btn bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-twitter mr-2"></i>
                            تويتر
                        </button>
                        <button onclick="shareLinkedIn()" 
                                class="share-btn bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-linkedin mr-2"></i>
                            لينكدإن
                        </button>
                        <button onclick="shareFacebook()" 
                                class="share-btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-facebook mr-2"></i>
                            فيسبوك
                        </button>
                    </div>
                </div>

                <!-- Domain Info -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">معلومات الدومين:</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <i class="fas fa-check-circle text-green-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">حالة الدومين</p>
                            <p class="text-lg font-bold text-green-600">نشط</p>
                        </div>
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <i class="fas fa-shield-alt text-blue-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">الأمان</p>
                            <p class="text-lg font-bold text-blue-600">HTTPS</p>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <i class="fas fa-globe text-purple-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">الوصول</p>
                            <p class="text-lg font-bold text-purple-600">عالمي</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 رفع ملفات HTML - دومين حقيقي. جميع الحقوق محفوظة.</p>
                <div class="mt-4 space-x-4 space-x-reverse">
                    <a href="#" class="hover:text-blue-500">سياسة الخصوصية</a>
                    <a href="#" class="hover:text-blue-500">تواصل معنا</a>
                    <a href="#" class="hover:text-blue-500">GitHub</a>
                </div>
                <p class="mt-4 text-sm text-gray-500">
                    <i class="fas fa-heart text-red-500"></i>
                    صُنع بحب لخدمة المطورين العرب
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let currentDomain = '';
        let currentFile = null;

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const resultSection = document.getElementById('resultSection');
        const deploymentSteps = document.getElementById('deploymentSteps');
        const realDomainInput = document.getElementById('realDomainInput');
        const displayDomain = document.getElementById('displayDomain');
        const finalDomain = document.getElementById('finalDomain');
        const qrContainer = document.getElementById('qrContainer');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
        });

        function setupEventListeners() {
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Click on drag area
            dragArea.addEventListener('click', () => fileInput.click());

            // Domain input events
            realDomainInput.addEventListener('input', updateDomainDisplay);
        }

        function updateDomainDisplay() {
            const domain = realDomainInput.value.trim();
            if (domain) {
                displayDomain.textContent = domain;
                currentDomain = domain;
            } else {
                displayDomain.textContent = 'لم يتم إدخال دومين بعد';
                currentDomain = '';
            }
        }

        function testDomain() {
            const domain = realDomainInput.value.trim();
            if (!domain) {
                alert('يرجى إدخال الدومين أولاً');
                return;
            }

            const testBtn = document.getElementById('testBtn');
            const resultDiv = document.getElementById('domainTestResult');

            // Show loading
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري الاختبار...';
            testBtn.disabled = true;

            // Test the domain by opening it in a new tab
            window.open(domain, '_blank');

            // Show result after a delay
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="flex items-center text-green-300">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span>تم فتح الدومين في تبويب جديد للاختبار</span>
                    </div>
                `;
                resultDiv.classList.remove('hidden');

                testBtn.innerHTML = '<i class="fas fa-external-link-alt mr-2"></i>اختبار الدومين';
                testBtn.disabled = false;
            }, 1000);
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                alert('يرجى اختيار ملف HTML فقط (.html أو .htm)');
                return;
            }

            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
                return;
            }

            // Check if domain is set
            if (!currentDomain) {
                alert('يرجى إدخال الدومين الحقيقي أولاً');
                realDomainInput.focus();
                return;
            }

            // Show file info
            showFileInfo(file);

            // Start deployment simulation
            startDeployment(file);
        }

        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
            currentFile = file;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function startDeployment(file) {
            // Show deployment steps
            deploymentSteps.classList.remove('hidden');

            // Simulate deployment process
            const steps = ['step1', 'step2', 'step3', 'step4'];
            let currentStep = 0;

            const interval = setInterval(() => {
                if (currentStep > 0) {
                    // Mark previous step as completed
                    document.getElementById(steps[currentStep - 1]).classList.add('completed');
                    document.getElementById(steps[currentStep - 1]).classList.remove('active');
                }

                if (currentStep < steps.length) {
                    // Mark current step as active
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    // All steps completed
                    document.getElementById(steps[steps.length - 1]).classList.add('completed');
                    document.getElementById(steps[steps.length - 1]).classList.remove('active');

                    clearInterval(interval);

                    // Update status
                    document.getElementById('deploymentStatus').innerHTML =
                        '<i class="fas fa-check-circle mr-2"></i>تم ربط الملف بالدومين بنجاح!';

                    // Show results after a delay
                    setTimeout(() => {
                        showResult();
                    }, 1000);
                }
            }, 1500);
        }

        function showResult() {
            try {
                // Hide deployment steps
                deploymentSteps.classList.add('hidden');

                // Set the final domain
                finalDomain.value = currentDomain;

                // Show result section
                resultSection.classList.add('show');

                // Generate QR code
                setTimeout(() => {
                    generateQRCode(currentDomain);
                }, 100);

                // Scroll to result
                setTimeout(() => {
                    resultSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }, 200);

                console.log('File linked to real domain successfully:', currentDomain);

            } catch (error) {
                console.error('Error showing result:', error);
                alert('حدث خطأ في عرض النتائج. يرجى المحاولة مرة أخرى.');
            }
        }

        function generateQRCode(url) {
            console.log('Generating QR for real domain:', url);

            // Method 1: Using Google Charts API
            const img = document.createElement('img');
            const encodedUrl = encodeURIComponent(url);
            img.src = `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodedUrl}`;
            img.alt = 'QR Code';
            img.className = 'w-48 h-48 border rounded-lg shadow-sm';

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('regenerateBtn').style.display = 'inline-block';
                console.log('QR Code generated successfully for real domain');
            };

            img.onerror = function() {
                console.log('Google Charts failed, trying alternative...');
                // Method 2: Using QR Server API
                const img2 = document.createElement('img');
                img2.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedUrl}`;
                img2.alt = 'QR Code';
                img2.className = 'w-48 h-48 border rounded-lg shadow-sm';

                img2.onload = function() {
                    qrContainer.innerHTML = '';
                    qrContainer.appendChild(img2);
                    document.getElementById('downloadBtn').style.display = 'inline-block';
                    document.getElementById('regenerateBtn').style.display = 'inline-block';
                    console.log('QR Code generated with alternative service');
                };

                img2.onerror = function() {
                    console.log('All QR services failed');
                    qrContainer.innerHTML = `
                        <div class="text-center p-6">
                            <i class="fas fa-qrcode text-6xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">لا يمكن توليد رمز QR تلقائياً</p>
                            <p class="text-sm text-gray-500 mb-4">يمكنك نسخ الدومين أعلاه واستخدامه في أي موقع لتوليد رمز QR</p>
                            <a href="https://www.qr-code-generator.com/" target="_blank"
                               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg inline-block">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                إنشاء رمز QR يدوياً
                            </a>
                        </div>
                    `;
                };
            };
        }

        function regenerateQR() {
            if (currentDomain) {
                qrContainer.innerHTML = `
                    <div class="text-gray-500 p-6">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>جاري إعادة توليد رمز QR...</p>
                    </div>
                `;
                document.getElementById('downloadBtn').style.display = 'none';
                document.getElementById('regenerateBtn').style.display = 'none';

                setTimeout(() => {
                    generateQRCode(currentDomain);
                }, 500);
            }
        }

        function downloadQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'real-domain-qr-code.png';
                link.href = img.src;
                link.click();
            } else {
                alert('لا يوجد رمز QR للتحميل');
            }
        }

        function copyDomain() {
            const domainInput = document.getElementById('finalDomain');

            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(domainInput.value).then(() => {
                    showCopyFeedback();
                }).catch(() => {
                    fallbackCopy();
                });
            } else {
                fallbackCopy();
            }

            function fallbackCopy() {
                domainInput.select();
                domainInput.setSelectionRange(0, 99999);
                document.execCommand('copy');
                showCopyFeedback();
            }

            function showCopyFeedback() {
                const button = event.target.closest('button');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.remove('bg-blue-500');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-blue-500');
                }, 2000);
            }
        }

        function openDomain() {
            window.open(finalDomain.value, '_blank');
        }

        // Share functions
        function shareWhatsApp() {
            const url = encodeURIComponent(finalDomain.value);
            const text = encodeURIComponent('شاهد موقعي الجديد: ');
            window.open(`https://wa.me/?text=${text}${url}`, '_blank');
        }

        function shareTwitter() {
            const url = encodeURIComponent(finalDomain.value);
            const text = encodeURIComponent('شاهد موقعي الجديد: ');
            window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
        }

        function shareLinkedIn() {
            const url = encodeURIComponent(finalDomain.value);
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
        }

        function shareFacebook() {
            const url = encodeURIComponent(finalDomain.value);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }
    </script>
</body>
</html>
