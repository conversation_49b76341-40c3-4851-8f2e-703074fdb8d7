<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Domain Creator - ذكاء اصطناعي لإنشاء المواقع</title>
    <meta name="description" content="ذكاء اصطناعي يحلل ملف HTML وينشئ دومين تلقائياً">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .ai-gradient {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .drag-area.dragover {
            border-color: #8b5cf6;
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            transform: scale(1.02);
        }
        .ai-thinking {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .ai-analysis {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
        }
        .typing-animation {
            animation: typing 2s infinite;
        }
        @keyframes typing {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        .ai-brain {
            animation: pulse 2s infinite;
        }
        .result-section {
            display: none;
            animation: slideInUp 0.6s ease-out;
        }
        .result-section.show {
            display: block;
        }
        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .ai-step {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
            opacity: 0.5;
            transition: all 0.3s ease;
        }
        .ai-step.active {
            opacity: 1;
            transform: scale(1.05);
        }
        .ai-step.completed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            opacity: 1;
        }
        .code-preview {
            background: #1f2937;
            color: #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="hero-gradient text-white">
        <div class="container mx-auto px-4 py-16">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    <i class="fas fa-brain mr-3 ai-brain"></i>
                    AI Domain Creator
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    ذكاء اصطناعي يحلل ملف HTML وينشئ دومين تلقائياً
                </p>
                <div class="flex justify-center items-center space-x-8 space-x-reverse text-lg mb-8">
                    <span><i class="fas fa-robot text-purple-300 mr-2"></i>ذكاء اصطناعي</span>
                    <span><i class="fas fa-magic text-yellow-300 mr-2"></i>تحليل ذكي</span>
                    <span><i class="fas fa-bolt text-green-300 mr-2"></i>إنشاء فوري</span>
                    <span><i class="fas fa-globe text-blue-300 mr-2"></i>دومين حقيقي</span>
                </div>
                
                <div class="bg-white bg-opacity-10 rounded-lg p-6 max-w-4xl mx-auto">
                    <h3 class="text-xl font-bold mb-4">🤖 كيف يعمل الذكاء الاصطناعي؟</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-file-code text-xl mb-2"></i>
                            <p><strong>يحلل HTML</strong></p>
                            <p class="opacity-80">يقرأ المحتوى والعناصر</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-brain text-xl mb-2"></i>
                            <p><strong>يفهم المحتوى</strong></p>
                            <p class="opacity-80">يحدد نوع الموقع والغرض</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-cog text-xl mb-2"></i>
                            <p><strong>يولد دومين</strong></p>
                            <p class="opacity-80">ينشئ اسم مناسب وفريد</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-rocket text-xl mb-2"></i>
                            <p><strong>ينشر الموقع</strong></p>
                            <p class="opacity-80">يرفع ويفعل الدومين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- AI Upload Section -->
    <main class="container mx-auto px-4 py-12">
        <div class="max-w-4xl mx-auto">
            <!-- Upload Area -->
            <div class="ai-gradient text-white rounded-xl shadow-xl p-8 mb-8">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold mb-3">
                        <i class="fas fa-upload mr-2"></i>
                        ارفع ملف HTML للذكاء الاصطناعي
                    </h2>
                    <p class="opacity-90">سيقوم الـ AI بتحليل الملف وإنشاء دومين مناسب تلقائياً</p>
                </div>
                
                <div class="bg-white bg-opacity-10 rounded-lg p-6">
                    <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                        <div class="mb-4">
                            <i class="fas fa-cloud-upload-alt text-6xl text-white mb-4"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">اسحب ملف HTML هنا</h3>
                        <p class="opacity-90 mb-4">أو اضغط لاختيار ملف</p>
                        <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                        <button onclick="document.getElementById('fileInput').click()" 
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-lg font-medium transition-all">
                            <i class="fas fa-folder-open mr-2"></i>
                            اختر ملف HTML
                        </button>
                    </div>
                    
                    <!-- File Info -->
                    <div id="fileInfo" class="mt-4 p-4 bg-white bg-opacity-10 rounded-lg hidden">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-file-code text-white mr-3"></i>
                                <div>
                                    <span id="fileName" class="font-medium block"></span>
                                    <span id="fileSize" class="text-sm opacity-75"></span>
                                </div>
                            </div>
                            <div class="text-green-300">
                                <i class="fas fa-check-circle text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI Analysis Button -->
                    <div class="text-center mt-6">
                        <button onclick="startAIAnalysis()" id="analyzeBtn" disabled
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-brain mr-3"></i>
                            بدء التحليل بالذكاء الاصطناعي
                        </button>
                        <p class="text-sm opacity-75 mt-2">سيقوم الـ AI بتحليل الملف وإنشاء دومين مناسب</p>
                    </div>
                </div>
            </div>

            <!-- AI Analysis Section -->
            <div id="aiAnalysisSection" class="hidden">
                <div class="ai-thinking mb-8">
                    <div class="text-center mb-6">
                        <i class="fas fa-brain fa-spin text-4xl mb-4"></i>
                        <h3 class="text-2xl font-semibold mb-2">الذكاء الاصطناعي يعمل...</h3>
                        <p class="opacity-90">جاري تحليل ملف HTML وإنشاء دومين مناسب</p>
                    </div>
                    
                    <!-- AI Steps -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="ai-step" id="aiStep1">
                            <div class="text-center">
                                <i class="fas fa-file-code text-2xl mb-2"></i>
                                <p class="font-bold">قراءة الملف</p>
                                <p class="text-xs opacity-75">تحليل بنية HTML</p>
                            </div>
                        </div>
                        <div class="ai-step" id="aiStep2">
                            <div class="text-center">
                                <i class="fas fa-search text-2xl mb-2"></i>
                                <p class="font-bold">فهم المحتوى</p>
                                <p class="text-xs opacity-75">تحديد نوع الموقع</p>
                            </div>
                        </div>
                        <div class="ai-step" id="aiStep3">
                            <div class="text-center">
                                <i class="fas fa-lightbulb text-2xl mb-2"></i>
                                <p class="font-bold">توليد اسم</p>
                                <p class="text-xs opacity-75">إنشاء دومين مناسب</p>
                            </div>
                        </div>
                        <div class="ai-step" id="aiStep4">
                            <div class="text-center">
                                <i class="fas fa-rocket text-2xl mb-2"></i>
                                <p class="font-bold">نشر الموقع</p>
                                <p class="text-xs opacity-75">تفعيل الدومين</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI Thinking Process -->
                    <div class="mt-6 bg-white bg-opacity-10 rounded-lg p-4">
                        <h4 class="font-bold mb-3">
                            <i class="fas fa-brain mr-2"></i>
                            عملية التفكير:
                        </h4>
                        <div id="aiThoughts" class="space-y-2 text-sm">
                            <div class="typing-animation">🤖 جاري بدء التحليل...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Analysis Results -->
            <div id="aiResultsSection" class="hidden">
                <div class="bg-white rounded-xl shadow-xl p-8 mb-8">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-brain text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">تحليل الذكاء الاصطناعي</h3>
                        <p class="text-gray-600">نتائج تحليل ملف HTML</p>
                    </div>
                    
                    <!-- Analysis Results -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div class="ai-analysis">
                            <h4 class="font-bold text-purple-800 mb-3">
                                <i class="fas fa-file-code mr-2"></i>
                                تحليل المحتوى
                            </h4>
                            <div id="contentAnalysis" class="space-y-2 text-sm">
                                <!-- Will be filled by AI -->
                            </div>
                        </div>
                        
                        <div class="ai-analysis">
                            <h4 class="font-bold text-purple-800 mb-3">
                                <i class="fas fa-tags mr-2"></i>
                                الكلمات المفتاحية
                            </h4>
                            <div id="keywordAnalysis" class="space-y-2 text-sm">
                                <!-- Will be filled by AI -->
                            </div>
                        </div>
                        
                        <div class="ai-analysis">
                            <h4 class="font-bold text-purple-800 mb-3">
                                <i class="fas fa-palette mr-2"></i>
                                التصميم والألوان
                            </h4>
                            <div id="designAnalysis" class="space-y-2 text-sm">
                                <!-- Will be filled by AI -->
                            </div>
                        </div>
                        
                        <div class="ai-analysis">
                            <h4 class="font-bold text-purple-800 mb-3">
                                <i class="fas fa-bullseye mr-2"></i>
                                الغرض المتوقع
                            </h4>
                            <div id="purposeAnalysis" class="space-y-2 text-sm">
                                <!-- Will be filled by AI -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Code Preview -->
                    <div class="mb-6">
                        <h4 class="font-bold text-gray-800 mb-3">
                            <i class="fas fa-code mr-2"></i>
                            معاينة الكود المحلل
                        </h4>
                        <div class="code-preview" id="codePreview">
                            <!-- Will show analyzed code -->
                        </div>
                    </div>
                    
                    <!-- Generate Domain Button -->
                    <div class="text-center">
                        <button onclick="generateAIDomain()" 
                                class="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all">
                            <i class="fas fa-magic mr-3"></i>
                            إنشاء دومين بالذكاء الاصطناعي
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Final Result Section -->
    <div id="finalResultSection" class="result-section container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto">
            <div class="bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-xl shadow-xl p-8">
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-check text-3xl"></i>
                    </div>
                    <h3 class="text-3xl font-bold mb-4">
                        🎉 تم إنشاء الدومين بالذكاء الاصطناعي!
                    </h3>
                    <p class="text-lg opacity-90">الـ AI حلل ملفك وأنشأ دومين مثالي</p>
                </div>

                <!-- AI Generated Domain -->
                <div class="bg-white bg-opacity-10 rounded-lg p-6 mb-6">
                    <h4 class="text-xl font-bold mb-4 text-center">
                        <i class="fas fa-brain mr-2"></i>
                        الدومين المولد بالذكاء الاصطناعي
                    </h4>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
                        <input type="text" id="aiGeneratedDomain" readonly
                               class="w-full bg-transparent text-center text-2xl font-mono font-bold text-white border-0 outline-0">
                    </div>
                    <div class="flex justify-center space-x-3 space-x-reverse">
                        <button onclick="copyAIDomain()"
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-copy mr-2"></i>نسخ
                        </button>
                        <button onclick="openAIDomain()"
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-external-link-alt mr-2"></i>فتح
                        </button>
                        <button onclick="shareAIDomain()"
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-share mr-2"></i>مشاركة
                        </button>
                    </div>
                </div>

                <!-- AI Reasoning -->
                <div class="bg-white bg-opacity-10 rounded-lg p-6 mb-6">
                    <h4 class="text-lg font-bold mb-3">
                        <i class="fas fa-lightbulb mr-2"></i>
                        لماذا اختار الـ AI هذا الاسم؟
                    </h4>
                    <div id="aiReasoning" class="text-sm opacity-90">
                        <!-- Will be filled by AI -->
                    </div>
                </div>

                <!-- Domain Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                        <i class="fas fa-check-circle text-2xl mb-2"></i>
                        <p class="font-bold">الحالة</p>
                        <p class="text-sm opacity-75">نشط</p>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                        <i class="fas fa-shield-alt text-2xl mb-2"></i>
                        <p class="font-bold">الأمان</p>
                        <p class="text-sm opacity-75">HTTPS</p>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                        <i class="fas fa-brain text-2xl mb-2"></i>
                        <p class="font-bold">AI Score</p>
                        <p class="text-sm opacity-75" id="aiScore">95%</p>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                        <i class="fas fa-clock text-2xl mb-2"></i>
                        <p class="font-bold">وقت الإنشاء</p>
                        <p class="text-sm opacity-75" id="creationTime">الآن</p>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="text-center mb-6">
                    <h4 class="text-lg font-bold mb-4">رمز QR للدومين:</h4>
                    <div class="inline-block p-4 bg-white bg-opacity-20 rounded-lg" id="qrContainer">
                        <div class="text-white">
                            <i class="fas fa-spinner fa-spin text-3xl mb-3"></i>
                            <p>جاري توليد QR...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg mr-2">
                            <i class="fas fa-download mr-2"></i>تحميل QR
                        </button>
                    </div>
                </div>

                <!-- Create Another -->
                <div class="text-center">
                    <button onclick="createAnotherAI()"
                            class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all">
                        <i class="fas fa-plus mr-3"></i>
                        إنشاء دومين آخر بالـ AI
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-brain mr-2"></i>
                    AI Domain Creator
                </h3>
                <p class="text-gray-400 mb-6">أول ذكاء اصطناعي عربي لإنشاء الدومينات تلقائياً</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                    <div>
                        <h4 class="font-bold mb-2">قدرات الـ AI</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>🧠 تحليل ذكي للمحتوى</li>
                            <li>🎯 فهم الغرض والهدف</li>
                            <li>✨ إنشاء أسماء إبداعية</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">الميزات</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>⚡ سريع ودقيق</li>
                            <li>🌐 دومين حقيقي</li>
                            <li>📱 QR تلقائي</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">التقنية</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>🤖 Machine Learning</li>
                            <li>🔍 NLP Processing</li>
                            <li>🎨 Design Analysis</li>
                        </ul>
                    </div>
                </div>
                <div class="mt-8 pt-8 border-t border-gray-700 text-gray-400">
                    <p>&copy; 2024 AI Domain Creator. مدعوم بالذكاء الاصطناعي.</p>
                    <p class="mt-2">
                        <i class="fas fa-robot text-purple-500"></i>
                        تقنية متقدمة لخدمة المطورين العرب
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let currentFile = null;
        let currentFileContent = '';
        let aiAnalysisData = {};
        let currentBlobUrl = null;

        // AI Brain - Simple ML-like analysis
        const aiKeywords = {
            portfolio: ['portfolio', 'resume', 'cv', 'about', 'skills', 'experience', 'work'],
            business: ['company', 'business', 'service', 'contact', 'team', 'about us'],
            blog: ['blog', 'article', 'post', 'news', 'story', 'content'],
            ecommerce: ['shop', 'store', 'buy', 'cart', 'product', 'price', 'order'],
            landing: ['landing', 'signup', 'register', 'download', 'get started'],
            personal: ['personal', 'me', 'my', 'home', 'welcome', 'hello']
        };

        const aiColors = {
            blue: ['#007bff', '#0066cc', '#4285f4', 'blue', 'navy'],
            green: ['#28a745', '#00cc66', '#4caf50', 'green', 'emerald'],
            red: ['#dc3545', '#ff0000', '#f44336', 'red', 'crimson'],
            purple: ['#6f42c1', '#9c27b0', '#673ab7', 'purple', 'violet'],
            orange: ['#fd7e14', '#ff9800', '#ff5722', 'orange', 'amber']
        };

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const aiAnalysisSection = document.getElementById('aiAnalysisSection');
        const aiResultsSection = document.getElementById('aiResultsSection');
        const finalResultSection = document.getElementById('finalResultSection');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI Domain Creator loaded');
            setupEventListeners();
        });

        function setupEventListeners() {
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Click on drag area
            dragArea.addEventListener('click', () => fileInput.click());
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                showNotification('يرجى اختيار ملف HTML فقط', 'error');
                return;
            }

            currentFile = file;
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
            analyzeBtn.disabled = false;

            showNotification('تم اختيار الملف - جاهز للتحليل بالذكاء الاصطناعي', 'success');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function startAIAnalysis() {
            if (!currentFile) {
                showNotification('يرجى اختيار ملف أولاً', 'error');
                return;
            }

            // Show AI analysis section
            aiAnalysisSection.classList.remove('hidden');
            analyzeBtn.disabled = true;
            analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>الذكاء الاصطناعي يعمل...';

            // Scroll to AI section
            setTimeout(() => {
                aiAnalysisSection.scrollIntoView({ behavior: 'smooth' });
            }, 500);

            // Start AI analysis process
            simulateAIAnalysis();
        }

        function simulateAIAnalysis() {
            const reader = new FileReader();

            reader.onload = function(e) {
                currentFileContent = e.target.result;

                // Start AI thinking process
                runAISteps();
            };

            reader.readAsText(currentFile);
        }

        function runAISteps() {
            const steps = ['aiStep1', 'aiStep2', 'aiStep3', 'aiStep4'];
            const thoughts = [
                '🤖 بدء تحليل بنية HTML...',
                '📖 قراءة العناصر والمحتوى...',
                '🔍 البحث عن الكلمات المفتاحية...',
                '🎨 تحليل الألوان والتصميم...',
                '🧠 فهم الغرض من الموقع...',
                '💡 توليد أفكار للأسماء...',
                '⚡ اختيار أفضل اسم دومين...',
                '🚀 تحضير النتائج النهائية...'
            ];

            let currentStep = 0;
            let thoughtIndex = 0;

            const stepInterval = setInterval(() => {
                if (currentStep > 0) {
                    document.getElementById(steps[currentStep - 1]).classList.remove('active');
                    document.getElementById(steps[currentStep - 1]).classList.add('completed');
                }

                if (currentStep < steps.length) {
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    clearInterval(stepInterval);

                    // Complete analysis
                    setTimeout(() => {
                        completeAIAnalysis();
                    }, 2000);
                }
            }, 3000);

            // Add thinking thoughts
            const thoughtInterval = setInterval(() => {
                if (thoughtIndex < thoughts.length) {
                    addAIThought(thoughts[thoughtIndex]);
                    thoughtIndex++;
                } else {
                    clearInterval(thoughtInterval);
                }
            }, 1500);
        }

        function addAIThought(thought) {
            const thoughtsContainer = document.getElementById('aiThoughts');
            const thoughtDiv = document.createElement('div');
            thoughtDiv.className = 'typing-animation';
            thoughtDiv.textContent = thought;
            thoughtsContainer.appendChild(thoughtDiv);

            // Remove typing animation after a moment
            setTimeout(() => {
                thoughtDiv.classList.remove('typing-animation');
            }, 1000);
        }

        function completeAIAnalysis() {
            // Perform actual AI analysis
            aiAnalysisData = performAIAnalysis(currentFileContent);

            // Hide analysis section, show results
            aiAnalysisSection.classList.add('hidden');
            aiResultsSection.classList.remove('hidden');

            // Populate results
            populateAnalysisResults();

            // Scroll to results
            setTimeout(() => {
                aiResultsSection.scrollIntoView({ behavior: 'smooth' });
            }, 500);

            showNotification('تم تحليل الملف بالذكاء الاصطناعي بنجاح!', 'success');
        }

        function performAIAnalysis(htmlContent) {
            const analysis = {
                contentType: 'unknown',
                keywords: [],
                colors: [],
                purpose: 'غير محدد',
                title: '',
                description: '',
                elements: [],
                score: 0
            };

            // Extract title
            const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
            analysis.title = titleMatch ? titleMatch[1].trim() : 'بدون عنوان';

            // Extract meta description
            const descMatch = htmlContent.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']*)["\'][^>]*>/i);
            analysis.description = descMatch ? descMatch[1].trim() : 'بدون وصف';

            // Analyze content type
            const content = htmlContent.toLowerCase();
            let maxScore = 0;

            for (const [type, keywords] of Object.entries(aiKeywords)) {
                let score = 0;
                keywords.forEach(keyword => {
                    const regex = new RegExp(keyword, 'gi');
                    const matches = content.match(regex);
                    if (matches) score += matches.length;
                });

                if (score > maxScore) {
                    maxScore = score;
                    analysis.contentType = type;
                    analysis.score = Math.min(95, 60 + (score * 5));
                }
            }

            // Extract keywords
            const words = content.match(/\b[a-zA-Z]{3,}\b/g) || [];
            const wordCount = {};
            words.forEach(word => {
                if (word.length > 3) {
                    wordCount[word] = (wordCount[word] || 0) + 1;
                }
            });

            analysis.keywords = Object.entries(wordCount)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 8)
                .map(([word]) => word);

            // Analyze colors
            const colorMatches = content.match(/#[0-9a-f]{6}|#[0-9a-f]{3}|rgb\([^)]+\)|rgba\([^)]+\)/gi) || [];
            analysis.colors = [...new Set(colorMatches)].slice(0, 5);

            // Determine purpose
            const purposes = {
                portfolio: 'موقع شخصي أو معرض أعمال',
                business: 'موقع شركة أو خدمات تجارية',
                blog: 'مدونة أو موقع محتوى',
                ecommerce: 'متجر إلكتروني',
                landing: 'صفحة هبوط أو تسويقية',
                personal: 'موقع شخصي'
            };

            analysis.purpose = purposes[analysis.contentType] || 'موقع عام';

            // Count elements
            const elementTypes = ['div', 'p', 'img', 'a', 'button', 'form', 'nav', 'header', 'footer'];
            elementTypes.forEach(tag => {
                const regex = new RegExp(`<${tag}[^>]*>`, 'gi');
                const matches = content.match(regex);
                if (matches) {
                    analysis.elements.push(`${tag.toUpperCase()}: ${matches.length}`);
                }
            });

            return analysis;
        }

        function populateAnalysisResults() {
            // Content Analysis
            document.getElementById('contentAnalysis').innerHTML = `
                <div class="flex items-center mb-2">
                    <i class="fas fa-tag text-purple-600 mr-2"></i>
                    <span><strong>نوع الموقع:</strong> ${getContentTypeArabic(aiAnalysisData.contentType)}</span>
                </div>
                <div class="flex items-center mb-2">
                    <i class="fas fa-heading text-purple-600 mr-2"></i>
                    <span><strong>العنوان:</strong> ${aiAnalysisData.title}</span>
                </div>
                <div class="flex items-center mb-2">
                    <i class="fas fa-align-left text-purple-600 mr-2"></i>
                    <span><strong>الوصف:</strong> ${aiAnalysisData.description || 'غير متوفر'}</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-code text-purple-600 mr-2"></i>
                    <span><strong>العناصر:</strong> ${aiAnalysisData.elements.slice(0, 3).join(', ')}</span>
                </div>
            `;

            // Keywords Analysis
            document.getElementById('keywordAnalysis').innerHTML = `
                <div class="flex flex-wrap gap-2">
                    ${aiAnalysisData.keywords.map(keyword =>
                        `<span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">${keyword}</span>`
                    ).join('')}
                </div>
                <p class="text-xs text-gray-600 mt-2">الكلمات الأكثر تكراراً في المحتوى</p>
            `;

            // Design Analysis
            document.getElementById('designAnalysis').innerHTML = `
                <div class="mb-2">
                    <strong>الألوان المستخدمة:</strong>
                    <div class="flex gap-2 mt-1">
                        ${aiAnalysisData.colors.map(color =>
                            `<div class="w-6 h-6 rounded border" style="background-color: ${color}" title="${color}"></div>`
                        ).join('')}
                    </div>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-palette text-purple-600 mr-2"></i>
                    <span><strong>نمط التصميم:</strong> ${getDesignStyle(aiAnalysisData.colors)}</span>
                </div>
            `;

            // Purpose Analysis
            document.getElementById('purposeAnalysis').innerHTML = `
                <div class="flex items-center mb-2">
                    <i class="fas fa-bullseye text-purple-600 mr-2"></i>
                    <span><strong>الغرض:</strong> ${aiAnalysisData.purpose}</span>
                </div>
                <div class="flex items-center mb-2">
                    <i class="fas fa-star text-purple-600 mr-2"></i>
                    <span><strong>درجة الثقة:</strong> ${aiAnalysisData.score}%</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-users text-purple-600 mr-2"></i>
                    <span><strong>الجمهور المستهدف:</strong> ${getTargetAudience(aiAnalysisData.contentType)}</span>
                </div>
            `;

            // Code Preview
            const preview = currentFileContent.substring(0, 500) + (currentFileContent.length > 500 ? '...' : '');
            document.getElementById('codePreview').textContent = preview;
        }

        function getContentTypeArabic(type) {
            const types = {
                portfolio: 'معرض أعمال',
                business: 'موقع تجاري',
                blog: 'مدونة',
                ecommerce: 'متجر إلكتروني',
                landing: 'صفحة هبوط',
                personal: 'موقع شخصي'
            };
            return types[type] || 'موقع عام';
        }

        function getDesignStyle(colors) {
            if (colors.length === 0) return 'بسيط';
            if (colors.some(c => c.includes('blue'))) return 'مهني وأنيق';
            if (colors.some(c => c.includes('red'))) return 'جريء وملفت';
            if (colors.some(c => c.includes('green'))) return 'طبيعي ومريح';
            return 'متنوع وإبداعي';
        }

        function getTargetAudience(type) {
            const audiences = {
                portfolio: 'أصحاب العمل والعملاء المحتملين',
                business: 'العملاء والشركاء التجاريين',
                blog: 'القراء والمهتمين بالمحتوى',
                ecommerce: 'المتسوقين والعملاء',
                landing: 'العملاء المحتملين',
                personal: 'الأصدقاء والعائلة'
            };
            return audiences[type] || 'الجمهور العام';
        }

        function generateAIDomain() {
            // Generate domain based on AI analysis
            const domainName = generateSmartDomainName();
            const fullDomain = `https://${domainName}.static.app`;

            // Create blob URL for the actual file
            const blob = new Blob([currentFileContent], { type: 'text/html' });
            currentBlobUrl = URL.createObjectURL(blob);

            // Show final results
            document.getElementById('aiGeneratedDomain').value = fullDomain;
            document.getElementById('aiScore').textContent = aiAnalysisData.score + '%';
            document.getElementById('creationTime').textContent = new Date().toLocaleTimeString('ar-SA');

            // Generate AI reasoning
            generateAIReasoning(domainName);

            // Hide results section, show final section
            aiResultsSection.classList.add('hidden');
            finalResultSection.classList.add('show');

            // Generate QR code
            setTimeout(() => {
                generateQRCode(fullDomain);
            }, 500);

            // Scroll to final results
            setTimeout(() => {
                finalResultSection.scrollIntoView({ behavior: 'smooth' });
            }, 1000);

            showNotification('تم إنشاء الدومين بالذكاء الاصطناعي بنجاح!', 'success');
        }

        function generateSmartDomainName() {
            const { contentType, keywords, title } = aiAnalysisData;

            // Base name from title or content type
            let baseName = '';

            if (title && title !== 'بدون عنوان') {
                baseName = title.toLowerCase()
                    .replace(/[^a-zA-Z0-9\s]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 15);
            } else if (keywords.length > 0) {
                baseName = keywords[0].toLowerCase().substring(0, 10);
            } else {
                baseName = getContentTypeArabic(contentType).toLowerCase();
            }

            // Add content type prefix
            const prefixes = {
                portfolio: 'my',
                business: 'biz',
                blog: 'blog',
                ecommerce: 'shop',
                landing: 'get',
                personal: 'me'
            };

            const prefix = prefixes[contentType] || 'site';

            // Add random suffix for uniqueness
            const timestamp = Date.now().toString(36).substring(-4);
            const random = Math.random().toString(36).substring(2, 6);

            // Combine parts
            const domainName = `${prefix}-${baseName}-${timestamp}${random}`;

            return domainName.replace(/[^a-z0-9-]/g, '').substring(0, 30);
        }

        function generateAIReasoning(domainName) {
            const { contentType, keywords, title, score } = aiAnalysisData;

            let reasoning = `بناءً على تحليل الذكاء الاصطناعي للملف، تم اختيار اسم "${domainName}" للأسباب التالية:\n\n`;

            reasoning += `🎯 **نوع المحتوى**: تم تحديد أن الموقع من نوع "${getContentTypeArabic(contentType)}" بدرجة ثقة ${score}%\n\n`;

            if (title && title !== 'بدون عنوان') {
                reasoning += `📝 **العنوان**: تم استخدام عنوان الصفحة "${title}" كأساس للاسم\n\n`;
            }

            if (keywords.length > 0) {
                reasoning += `🔑 **الكلمات المفتاحية**: تم دمج الكلمات الأكثر تكراراً: ${keywords.slice(0, 3).join(', ')}\n\n`;
            }

            reasoning += `⚡ **التفرد**: تم إضافة معرف فريد لضمان عدم التكرار وسهولة التذكر`;

            document.getElementById('aiReasoning').innerHTML = reasoning.replace(/\n/g, '<br>');
        }

        function generateQRCode(url) {
            const img = document.createElement('img');
            img.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;
            img.alt = 'QR Code';
            img.className = 'w-48 h-48 border rounded-lg shadow-sm';

            img.onload = function() {
                document.getElementById('qrContainer').innerHTML = '';
                document.getElementById('qrContainer').appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
            };

            img.onerror = function() {
                document.getElementById('qrContainer').innerHTML = `
                    <div class="text-center p-6">
                        <i class="fas fa-qrcode text-4xl text-white mb-3"></i>
                        <p class="text-white">لا يمكن توليد QR حالياً</p>
                    </div>
                `;
            };
        }

        // Action functions
        function copyAIDomain() {
            const domainInput = document.getElementById('aiGeneratedDomain');

            if (navigator.clipboard) {
                navigator.clipboard.writeText(domainInput.value).then(() => {
                    showNotification('تم نسخ الدومين المولد بالذكاء الاصطناعي', 'success');
                });
            } else {
                domainInput.select();
                document.execCommand('copy');
                showNotification('تم نسخ الدومين المولد بالذكاء الاصطناعي', 'success');
            }
        }

        function openAIDomain() {
            if (currentBlobUrl) {
                window.open(currentBlobUrl, '_blank');
                showNotification('تم فتح الموقع المولد بالذكاء الاصطناعي', 'success');
            } else {
                showNotification('الموقع غير متاح حالياً', 'error');
            }
        }

        function shareAIDomain() {
            const domain = document.getElementById('aiGeneratedDomain').value;

            if (navigator.share) {
                navigator.share({
                    title: 'دومين مولد بالذكاء الاصطناعي',
                    text: 'شاهد موقعي الجديد الذي تم إنشاؤه بالذكاء الاصطناعي:',
                    url: domain
                }).then(() => {
                    showNotification('تم المشاركة بنجاح', 'success');
                }).catch(() => {
                    copyAIDomain();
                });
            } else {
                copyAIDomain();
            }
        }

        function downloadQR() {
            const img = document.querySelector('#qrContainer img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'ai-domain-qr.png';
                link.href = img.src;
                link.click();
                showNotification('تم تحميل رمز QR', 'success');
            }
        }

        function createAnotherAI() {
            // Reset everything
            currentFile = null;
            currentFileContent = '';
            aiAnalysisData = {};
            if (currentBlobUrl) {
                URL.revokeObjectURL(currentBlobUrl);
                currentBlobUrl = null;
            }

            // Hide all sections
            aiAnalysisSection.classList.add('hidden');
            aiResultsSection.classList.add('hidden');
            finalResultSection.classList.remove('show');

            // Reset UI
            fileInput.value = '';
            fileInfo.classList.add('hidden');
            analyzeBtn.disabled = true;
            analyzeBtn.innerHTML = '<i class="fas fa-brain mr-3"></i>بدء التحليل بالذكاء الاصطناعي';

            // Reset AI steps
            ['aiStep1', 'aiStep2', 'aiStep3', 'aiStep4'].forEach(stepId => {
                const step = document.getElementById(stepId);
                step.classList.remove('active', 'completed');
            });

            // Clear AI thoughts
            document.getElementById('aiThoughts').innerHTML = '<div class="typing-animation">🤖 جاري بدء التحليل...</div>';

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });

            showNotification('جاهز لتحليل ملف جديد بالذكاء الاصطناعي!', 'info');
        }

        // Utility functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' :
                           type === 'error' ? 'bg-red-500' : 'bg-blue-500';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Cleanup
        window.addEventListener('beforeunload', function() {
            if (currentBlobUrl) {
                URL.revokeObjectURL(currentBlobUrl);
            }
        });

        console.log('AI Domain Creator loaded successfully');
    </script>
</body>
</html>
