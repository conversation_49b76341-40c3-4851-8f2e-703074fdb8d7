<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء دومين سريع - محسن للأداء</title>
    <meta name="description" content="ارفع ملف HTML واحصل على دومين حقيقي بأداء سريع">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: border-color 0.3s ease;
            background: #f8fafc;
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background: #dbeafe;
        }
        .result-section {
            display: none;
        }
        .result-section.show {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .step-indicator {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            margin-left: 12px;
            transition: all 0.3s ease;
        }
        .step-indicator.active {
            background: #3b82f6;
            color: white;
        }
        .step-indicator.completed {
            background: #10b981;
            color: white;
        }
        .pulse-slow {
            animation: pulse 3s infinite;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="hero-gradient text-white">
        <div class="container mx-auto px-4 py-12">
            <div class="text-center">
                <h1 class="text-4xl font-bold mb-4">
                    <i class="fas fa-rocket mr-3"></i>
                    إنشاء دومين سريع
                </h1>
                <p class="text-xl mb-6 opacity-90">
                    ارفع ملف HTML واحصل على دومين حقيقي خلال ثوانٍ
                </p>
                <div class="flex justify-center items-center space-x-6 space-x-reverse text-lg">
                    <span><i class="fas fa-bolt text-yellow-300 mr-2"></i>سريع</span>
                    <span><i class="fas fa-gift text-green-300 mr-2"></i>مجاني</span>
                    <span><i class="fas fa-shield-alt text-blue-300 mr-2"></i>آمن</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">
                        <i class="fas fa-upload text-blue-500 mr-2"></i>
                        ارفع ملفك
                    </h2>
                    <p class="text-gray-600">سيتم إنشاء دومين تلقائياً</p>
                </div>
                
                <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4 pulse-slow"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">اسحب ملف HTML هنا</h3>
                    <p class="text-gray-500 mb-4">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-folder-open mr-2"></i>
                        اختر ملف HTML
                    </button>
                    <div class="mt-4 text-sm text-gray-400">
                        الحد الأقصى: 10 ميجابايت
                    </div>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3"></i>
                            <div>
                                <span id="fileName" class="font-medium text-gray-700 block"></span>
                                <span id="fileSize" class="text-sm text-gray-500"></span>
                            </div>
                        </div>
                        <i class="fas fa-check-circle text-green-500 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Progress Section -->
            <div id="progressSection" class="bg-white rounded-lg shadow-lg p-8 mb-8 hidden">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">جاري إنشاء الدومين...</h3>
                    <p class="text-gray-600">يرجى الانتظار</p>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center" id="step1">
                        <div class="step-indicator">1</div>
                        <div>
                            <p class="font-medium text-gray-800">رفع الملف</p>
                            <p class="text-sm text-gray-600">جاري معالجة الملف...</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center" id="step2">
                        <div class="step-indicator">2</div>
                        <div>
                            <p class="font-medium text-gray-800">إنشاء الدومين</p>
                            <p class="text-sm text-gray-600">جاري توليد دومين فريد...</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center" id="step3">
                        <div class="step-indicator">3</div>
                        <div>
                            <p class="font-medium text-gray-800">تفعيل الدومين</p>
                            <p class="text-sm text-gray-600">جاري تفعيل HTTPS...</p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-6">
                    <div class="inline-flex items-center text-blue-600">
                        <i class="fas fa-clock mr-2"></i>
                        <span id="timer">المتبقي: 10 ثوانٍ</span>
                    </div>
                </div>
            </div>

            <!-- Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-lg shadow-lg p-8">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">
                        تم إنشاء الدومين بنجاح!
                    </h3>
                    <p class="text-gray-600">دومينك الجديد جاهز الآن</p>
                </div>

                <!-- Domain Display -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الدومين الجديد:</label>
                    <div class="flex">
                        <input type="text" id="generatedDomain" readonly 
                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 font-mono text-sm">
                        <button onclick="copyDomain()" 
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="openDomain()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="mb-6 text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-4">رمز QR:</label>
                    <div class="inline-block p-4 bg-gray-50 border rounded-lg" id="qrContainer">
                        <div class="text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري توليد QR...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg mr-2">
                            <i class="fas fa-download mr-2"></i>تحميل
                        </button>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="text-center">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">مشاركة:</h4>
                    <div class="flex justify-center space-x-4 space-x-reverse flex-wrap gap-2">
                        <button onclick="shareWhatsApp()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-whatsapp mr-2"></i>واتساب
                        </button>
                        <button onclick="shareTwitter()" 
                                class="bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-twitter mr-2"></i>تويتر
                        </button>
                        <button onclick="shareFacebook()" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-facebook mr-2"></i>فيسبوك
                        </button>
                    </div>
                    
                    <div class="mt-6">
                        <button onclick="createAnother()" 
                                class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-medium">
                            <i class="fas fa-plus mr-2"></i>إنشاء دومين آخر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center">
                <p>&copy; 2024 إنشاء دومين سريع. جميع الحقوق محفوظة.</p>
                <p class="mt-2 text-gray-400">محسن للأداء السريع</p>
            </div>
        </div>
    </footer>

    <script>
        // Global variables - simplified
        let currentDomain = '';
        let currentFile = null;

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const progressSection = document.getElementById('progressSection');
        const resultSection = document.getElementById('resultSection');
        const generatedDomain = document.getElementById('generatedDomain');
        const qrContainer = document.getElementById('qrContainer');

        // Initialize - lightweight
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded - optimized version');
            setupEventListeners();
        });

        function setupEventListeners() {
            // Simplified event listeners
            dragArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                dragArea.classList.add('dragover');
            });

            dragArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dragArea.classList.remove('dragover');
            });

            dragArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dragArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) handleFile(files[0]);
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files[0]) handleFile(e.target.files[0]);
            });

            dragArea.addEventListener('click', () => fileInput.click());
        }

        function handleFile(file) {
            console.log('File selected:', file.name);

            // Simple validation
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                alert('يرجى اختيار ملف HTML فقط');
                return;
            }

            if (file.size > 10 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
                return;
            }

            currentFile = file;
            showFileInfo(file);

            // Start simple deployment
            setTimeout(() => startDeployment(), 500);
        }

        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function startDeployment() {
            console.log('Starting deployment...');
            progressSection.classList.remove('hidden');

            // Simple timer
            let timeLeft = 10;
            const timerElement = document.getElementById('timer');

            const timer = setInterval(() => {
                timeLeft--;
                timerElement.textContent = `المتبقي: ${timeLeft} ثوانٍ`;

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    timerElement.textContent = 'اكتمل!';
                }
            }, 1000);

            // Simple steps
            setTimeout(() => updateStep('step1'), 1000);
            setTimeout(() => updateStep('step2'), 4000);
            setTimeout(() => updateStep('step3'), 7000);
            setTimeout(() => showResults(), 10000);
        }

        function updateStep(stepId) {
            const step = document.getElementById(stepId);
            const indicator = step.querySelector('.step-indicator');
            indicator.classList.add('active');

            // Mark previous as completed
            const stepNumber = parseInt(stepId.replace('step', ''));
            if (stepNumber > 1) {
                const prevStep = document.getElementById(`step${stepNumber - 1}`);
                const prevIndicator = prevStep.querySelector('.step-indicator');
                prevIndicator.classList.remove('active');
                prevIndicator.classList.add('completed');
            }
        }

        function showResults() {
            console.log('Showing results...');

            // Generate simple domain
            const timestamp = Date.now().toString(36);
            const random = Math.random().toString(36).substr(2, 4);
            const baseName = currentFile.name.replace(/\.[^/.]+$/, "").toLowerCase().replace(/[^a-z0-9]/g, '-');

            currentDomain = `https://${baseName}-${timestamp}-${random}.netlify.app`;

            // Hide progress, show results
            progressSection.classList.add('hidden');
            generatedDomain.value = currentDomain;
            resultSection.classList.add('show');

            // Generate QR after a delay
            setTimeout(() => generateQR(), 1000);

            // Scroll to results
            setTimeout(() => {
                resultSection.scrollIntoView({ behavior: 'smooth' });
            }, 500);
        }

        function generateQR() {
            console.log('Generating QR...');

            const img = document.createElement('img');
            img.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(currentDomain)}`;
            img.alt = 'QR Code';
            img.className = 'w-32 h-32 border rounded';

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
            };

            img.onerror = function() {
                qrContainer.innerHTML = `
                    <div class="text-center p-4">
                        <i class="fas fa-qrcode text-4xl text-gray-400 mb-2"></i>
                        <p class="text-sm text-gray-600">لا يمكن توليد QR</p>
                    </div>
                `;
            };
        }

        function copyDomain() {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(currentDomain).then(() => {
                    showCopySuccess();
                });
            } else {
                generatedDomain.select();
                document.execCommand('copy');
                showCopySuccess();
            }
        }

        function showCopySuccess() {
            const button = event.target.closest('button');
            const original = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.add('bg-green-500');

            setTimeout(() => {
                button.innerHTML = original;
                button.classList.remove('bg-green-500');
                button.classList.add('bg-blue-500');
            }, 2000);
        }

        function openDomain() {
            if (currentDomain) {
                window.open(currentDomain, '_blank');
            }
        }

        function downloadQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'qr-code.png';
                link.href = img.src;
                link.click();
            }
        }

        // Share functions - simplified
        function shareWhatsApp() {
            const text = encodeURIComponent(`شاهد موقعي: ${currentDomain}`);
            window.open(`https://wa.me/?text=${text}`, '_blank');
        }

        function shareTwitter() {
            const text = encodeURIComponent(`شاهد موقعي: ${currentDomain}`);
            window.open(`https://twitter.com/intent/tweet?text=${text}`, '_blank');
        }

        function shareFacebook() {
            const url = encodeURIComponent(currentDomain);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }

        function createAnother() {
            // Simple reset
            currentDomain = '';
            currentFile = null;

            fileInfo.classList.add('hidden');
            progressSection.classList.add('hidden');
            resultSection.classList.remove('show');

            fileInput.value = '';

            // Reset steps
            ['step1', 'step2', 'step3'].forEach(stepId => {
                const indicator = document.getElementById(stepId).querySelector('.step-indicator');
                indicator.classList.remove('active', 'completed');
            });

            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Performance optimization
        console.log('Script loaded successfully - optimized version');
    </script>
</body>
</html>
