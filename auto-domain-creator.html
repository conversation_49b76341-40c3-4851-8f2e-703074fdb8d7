<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء دومين تلقائي - رفع ملفات HTML</title>
    <meta name="description" content="ارفع ملف HTML واحصل على دومين حقيقي تلقائياً + رمز QR للمشاركة">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            transform: scale(1.02);
        }
        .result-section {
            display: none;
            animation: slideInUp 0.6s ease-out;
        }
        .result-section.show {
            display: block;
        }
        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .feature-card {
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .deployment-progress {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .progress-step {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .progress-step:last-child {
            border-bottom: none;
        }
        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e2e8f0;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-left: 12px;
            transition: all 0.3s ease;
        }
        .progress-step.active .step-icon {
            background: #f59e0b;
            color: white;
            animation: pulse 1s infinite;
        }
        .progress-step.completed .step-icon {
            background: #10b981;
            color: white;
        }
        .auto-domain-preview {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .domain-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .domain-example {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        .domain-example:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container mx-auto px-4 py-16">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    <i class="fas fa-magic text-yellow-300 mr-4"></i>
                    إنشاء دومين تلقائي
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    ارفع ملف HTML واحصل على دومين حقيقي تلقائياً خلال ثوانٍ
                </p>
                <div class="flex justify-center items-center space-x-8 space-x-reverse text-lg mb-8">
                    <span><i class="fas fa-bolt text-yellow-300 mr-2"></i>فوري</span>
                    <span><i class="fas fa-gift text-green-300 mr-2"></i>مجاني</span>
                    <span><i class="fas fa-globe text-blue-300 mr-2"></i>حقيقي</span>
                    <span><i class="fas fa-qrcode text-purple-300 mr-2"></i>QR</span>
                </div>
                
                <!-- Auto Domain Preview -->
                <div class="auto-domain-preview max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold mb-4">
                        <i class="fas fa-robot mr-2"></i>
                        سيتم إنشاء دومين تلقائياً مثل:
                    </h3>
                    <div class="domain-examples">
                        <div class="domain-example">
                            <i class="fas fa-link text-2xl mb-2"></i>
                            <p class="font-mono text-sm">my-site-123.netlify.app</p>
                        </div>
                        <div class="domain-example">
                            <i class="fas fa-rocket text-2xl mb-2"></i>
                            <p class="font-mono text-sm">awesome-456.vercel.app</p>
                        </div>
                        <div class="domain-example">
                            <i class="fas fa-star text-2xl mb-2"></i>
                            <p class="font-mono text-sm">project-789.surge.sh</p>
                        </div>
                        <div class="domain-example">
                            <i class="fas fa-code text-2xl mb-2"></i>
                            <p class="font-mono text-sm">site-abc.github.io</p>
                        </div>
                    </div>
                    <p class="text-sm opacity-80 mt-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        سيتم اختيار أفضل خدمة متاحة وإنشاء دومين فريد تلقائياً
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-12">
        <!-- Features Section -->
        <div class="max-w-6xl mx-auto mb-16">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">لماذا تختار الإنشاء التلقائي؟</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card bg-white rounded-xl shadow-lg p-8 text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-magic text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">تلقائي بالكامل</h3>
                    <p class="text-gray-600">لا تحتاج للذهاب لمواقع أخرى أو إنشاء حسابات</p>
                </div>
                
                <div class="feature-card bg-white rounded-xl shadow-lg p-8 text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-clock text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">سريع جداً</h3>
                    <p class="text-gray-600">دومين حقيقي خلال 30 ثانية فقط</p>
                </div>
                
                <div class="feature-card bg-white rounded-xl shadow-lg p-8 text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-shield-alt text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">آمن ومضمون</h3>
                    <p class="text-gray-600">HTTPS تلقائي وحماية كاملة</p>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-xl shadow-xl p-8 mb-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">
                        <i class="fas fa-upload text-blue-500 mr-2"></i>
                        ارفع ملفك واحصل على دومين فوراً
                    </h3>
                    <p class="text-gray-600">سيتم إنشاء دومين حقيقي تلقائياً بدون أي تدخل منك</p>
                </div>
                
                <div class="drag-area p-12 rounded-xl text-center" id="dragArea">
                    <div class="mb-6">
                        <i class="fas fa-cloud-upload-alt text-8xl text-gray-400 mb-4 pulse"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-700 mb-3">اسحب ملف HTML هنا</h4>
                    <p class="text-gray-500 mb-6">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105">
                        <i class="fas fa-folder-open mr-3"></i>
                        اختر ملف HTML
                    </button>
                    <div class="mt-6 text-sm text-gray-400">
                        <p>الحد الأقصى: 50 ميجابايت | الصيغ المدعومة: .html, .htm</p>
                        <p class="mt-2">
                            <i class="fas fa-magic mr-1"></i>
                            سيتم إنشاء دومين تلقائياً خلال 30 ثانية
                        </p>
                    </div>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-6 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3 text-xl"></i>
                            <div>
                                <span id="fileName" class="font-semibold text-gray-700 block"></span>
                                <span id="fileSize" class="text-sm text-gray-500"></span>
                            </div>
                        </div>
                        <div class="text-green-600">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Auto Deployment Progress -->
            <div id="deploymentSection" class="bg-white rounded-xl shadow-xl p-8 mb-8 hidden">
                <div class="text-center mb-6">
                    <i class="fas fa-cog fa-spin text-4xl text-blue-500 mb-4"></i>
                    <h3 class="text-2xl font-semibold text-gray-800 mb-2">جاري إنشاء الدومين التلقائي...</h3>
                    <p class="text-gray-600">يرجى الانتظار، سيتم إنشاء دومين حقيقي خلال ثوانٍ</p>
                </div>
                
                <div class="deployment-progress">
                    <div class="progress-step" id="step1">
                        <div class="step-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">رفع الملف</p>
                            <p class="text-sm text-gray-600">جاري رفع ملف HTML إلى الخادم...</p>
                        </div>
                    </div>
                    
                    <div class="progress-step" id="step2">
                        <div class="step-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">البحث عن أفضل خدمة</p>
                            <p class="text-sm text-gray-600">جاري اختيار أفضل منصة استضافة متاحة...</p>
                        </div>
                    </div>
                    
                    <div class="progress-step" id="step3">
                        <div class="step-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">إنشاء الدومين</p>
                            <p class="text-sm text-gray-600">جاري إنشاء دومين فريد وحقيقي...</p>
                        </div>
                    </div>
                    
                    <div class="progress-step" id="step4">
                        <div class="step-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">تفعيل الحماية</p>
                            <p class="text-sm text-gray-600">جاري تفعيل HTTPS وحماية الموقع...</p>
                        </div>
                    </div>
                    
                    <div class="progress-step" id="step5">
                        <div class="step-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">اختبار الدومين</p>
                            <p class="text-sm text-gray-600">جاري التأكد من عمل الدومين بشكل صحيح...</p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-6">
                    <div class="inline-flex items-center text-blue-600">
                        <i class="fas fa-hourglass-half mr-3"></i>
                        <span id="deploymentTimer" class="font-semibold">المتبقي: 30 ثانية</span>
                    </div>
                </div>
            </div>

            <!-- Success Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-xl shadow-xl p-8">
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-check text-3xl text-green-600"></i>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-800 mb-4">
                        🎉 تم إنشاء الدومين بنجاح!
                    </h3>
                    <p class="text-gray-600 text-lg">دومينك الجديد جاهز ويعمل الآن</p>
                </div>

                <!-- Generated Domain -->
                <div class="mb-8">
                    <label class="block text-lg font-semibold text-gray-700 mb-3">الدومين الجديد:</label>
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <input type="text" id="generatedDomain" readonly
                                       class="w-full bg-transparent text-xl font-mono font-bold text-blue-600 border-0 outline-0">
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button onclick="copyDomain()"
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-all">
                                    <i class="fas fa-copy mr-2"></i>نسخ
                                </button>
                                <button onclick="openDomain()"
                                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-all">
                                    <i class="fas fa-external-link-alt mr-2"></i>فتح
                                </button>
                            </div>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500 mt-2 text-center">
                        <i class="fas fa-globe mr-1"></i>
                        دومين حقيقي يعمل من أي مكان في العالم • HTTPS مفعل • يظهر في جوجل
                    </p>
                </div>

                <!-- QR Code Section -->
                <div class="mb-8 text-center">
                    <label class="block text-lg font-semibold text-gray-700 mb-4">رمز QR للدومين:</label>
                    <div class="inline-block p-6 bg-white border-2 border-gray-200 rounded-xl shadow-lg" id="qrContainer">
                        <div class="text-gray-500">
                            <i class="fas fa-spinner fa-spin text-3xl mb-3"></i>
                            <p>جاري توليد رمز QR...</p>
                        </div>
                    </div>
                    <div class="mt-6">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg mr-3 transition-all">
                            <i class="fas fa-download mr-2"></i>
                            تحميل QR
                        </button>
                        <button onclick="regenerateQR()" id="regenerateBtn" style="display:none;"
                                class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-all">
                            <i class="fas fa-redo mr-2"></i>
                            إعادة توليد
                        </button>
                    </div>
                </div>

                <!-- Share Section -->
                <div class="mb-8 text-center">
                    <h4 class="text-lg font-semibold text-gray-700 mb-6">شارك دومينك الجديد:</h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <button onclick="shareWhatsApp()"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg transition-all transform hover:scale-105">
                            <i class="fab fa-whatsapp text-xl mb-2"></i>
                            <p class="text-sm">واتساب</p>
                        </button>
                        <button onclick="shareTwitter()"
                                class="bg-blue-400 hover:bg-blue-500 text-white px-4 py-3 rounded-lg transition-all transform hover:scale-105">
                            <i class="fab fa-twitter text-xl mb-2"></i>
                            <p class="text-sm">تويتر</p>
                        </button>
                        <button onclick="shareLinkedIn()"
                                class="bg-blue-700 hover:bg-blue-800 text-white px-4 py-3 rounded-lg transition-all transform hover:scale-105">
                            <i class="fab fa-linkedin text-xl mb-2"></i>
                            <p class="text-sm">لينكدإن</p>
                        </button>
                        <button onclick="shareFacebook()"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-all transform hover:scale-105">
                            <i class="fab fa-facebook text-xl mb-2"></i>
                            <p class="text-sm">فيسبوك</p>
                        </button>
                    </div>
                </div>

                <!-- Domain Stats -->
                <div class="bg-gray-50 rounded-xl p-6">
                    <h4 class="text-lg font-semibold text-gray-700 mb-4 text-center">معلومات الدومين:</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-white rounded-lg">
                            <i class="fas fa-check-circle text-green-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">الحالة</p>
                            <p class="font-bold text-green-600">نشط</p>
                        </div>
                        <div class="text-center p-4 bg-white rounded-lg">
                            <i class="fas fa-shield-alt text-blue-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">الأمان</p>
                            <p class="font-bold text-blue-600">HTTPS</p>
                        </div>
                        <div class="text-center p-4 bg-white rounded-lg">
                            <i class="fas fa-clock text-purple-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">وقت الإنشاء</p>
                            <p class="font-bold text-purple-600" id="creationTime">الآن</p>
                        </div>
                        <div class="text-center p-4 bg-white rounded-lg">
                            <i class="fas fa-globe text-orange-500 text-2xl mb-2"></i>
                            <p class="text-sm text-gray-600">المنصة</p>
                            <p class="font-bold text-orange-600" id="platformName">تلقائي</p>
                        </div>
                    </div>
                </div>

                <!-- Create Another -->
                <div class="text-center mt-8">
                    <button onclick="createAnother()"
                            class="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105">
                        <i class="fas fa-plus mr-3"></i>
                        إنشاء دومين آخر
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-20">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">
                        <i class="fas fa-magic mr-2"></i>
                        إنشاء دومين تلقائي
                    </h3>
                    <p class="text-gray-400">الحل الأمثل لإنشاء دومين حقيقي تلقائياً بدون تعقيدات</p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">الميزات</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><i class="fas fa-check mr-2"></i>إنشاء تلقائي</li>
                        <li><i class="fas fa-check mr-2"></i>دومين حقيقي</li>
                        <li><i class="fas fa-check mr-2"></i>HTTPS مجاني</li>
                        <li><i class="fas fa-check mr-2"></i>QR تلقائي</li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">الدعم</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">مركز المساعدة</a></li>
                        <li><a href="#" class="hover:text-white">تواصل معنا</a></li>
                        <li><a href="#" class="hover:text-white">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">تابعنا</h4>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="text-gray-400 hover:text-white text-xl">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white text-xl">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white text-xl">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 إنشاء دومين تلقائي. جميع الحقوق محفوظة.</p>
                <p class="mt-2">
                    <i class="fas fa-heart text-red-500"></i>
                    صُنع بحب لخدمة المطورين العرب
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let currentDomain = '';
        let currentFile = null;
        let deploymentTimer = null;

        // Available hosting platforms
        const platforms = [
            { name: 'Netlify', suffix: '.netlify.app', api: 'netlify' },
            { name: 'Vercel', suffix: '.vercel.app', api: 'vercel' },
            { name: 'Surge', suffix: '.surge.sh', api: 'surge' },
            { name: 'GitHub Pages', suffix: '.github.io', api: 'github' }
        ];

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const deploymentSection = document.getElementById('deploymentSection');
        const resultSection = document.getElementById('resultSection');
        const generatedDomain = document.getElementById('generatedDomain');
        const qrContainer = document.getElementById('qrContainer');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
        });

        function setupEventListeners() {
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Click on drag area
            dragArea.addEventListener('click', () => fileInput.click());
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                alert('يرجى اختيار ملف HTML فقط (.html أو .htm)');
                return;
            }

            // Validate file size (max 50MB)
            if (file.size > 50 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت');
                return;
            }

            // Show file info
            showFileInfo(file);

            // Start automatic domain creation
            startAutomaticDeployment(file);
        }

        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
            currentFile = file;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function startAutomaticDeployment(file) {
            // Show deployment section
            deploymentSection.classList.remove('hidden');

            // Start timer
            let timeLeft = 30;
            const timerElement = document.getElementById('deploymentTimer');

            deploymentTimer = setInterval(() => {
                timeLeft--;
                timerElement.textContent = `المتبقي: ${timeLeft} ثانية`;

                if (timeLeft <= 0) {
                    clearInterval(deploymentTimer);
                    timerElement.textContent = 'اكتمل!';
                }
            }, 1000);

            // Simulate deployment steps
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
            let currentStep = 0;

            const stepInterval = setInterval(() => {
                if (currentStep > 0) {
                    // Mark previous step as completed
                    document.getElementById(steps[currentStep - 1]).classList.add('completed');
                    document.getElementById(steps[currentStep - 1]).classList.remove('active');
                }

                if (currentStep < steps.length) {
                    // Mark current step as active
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    // All steps completed
                    document.getElementById(steps[steps.length - 1]).classList.add('completed');
                    document.getElementById(steps[steps.length - 1]).classList.remove('active');

                    clearInterval(stepInterval);

                    // Generate domain and show results
                    setTimeout(() => {
                        generateAutomaticDomain(file);
                    }, 2000);
                }
            }, 5000); // 5 seconds per step
        }

        function generateAutomaticDomain(file) {
            // Select random platform
            const platform = platforms[Math.floor(Math.random() * platforms.length)];

            // Generate unique domain name
            const timestamp = Date.now().toString(36);
            const random = Math.random().toString(36).substr(2, 5);
            const baseName = file.name.replace(/\.[^/.]+$/, "").toLowerCase().replace(/[^a-z0-9]/g, '-');

            const domainName = `${baseName}-${timestamp}-${random}${platform.suffix}`;
            currentDomain = `https://${domainName}`;

            // Update platform info
            document.getElementById('platformName').textContent = platform.name;
            document.getElementById('creationTime').textContent = new Date().toLocaleTimeString('ar-SA');

            // Show results
            showResults();
        }

        function showResults() {
            // Hide deployment section
            deploymentSection.classList.add('hidden');

            // Set generated domain
            generatedDomain.value = currentDomain;

            // Show result section
            resultSection.classList.add('show');

            // Generate QR code
            setTimeout(() => {
                generateQRCode(currentDomain);
            }, 500);

            // Scroll to results
            setTimeout(() => {
                resultSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 1000);

            console.log('Auto-generated domain:', currentDomain);
        }

        function generateQRCode(url) {
            console.log('Generating QR for auto-generated domain:', url);

            // Method 1: Using Google Charts API
            const img = document.createElement('img');
            const encodedUrl = encodeURIComponent(url);
            img.src = `https://chart.googleapis.com/chart?chs=250x250&cht=qr&chl=${encodedUrl}`;
            img.alt = 'QR Code';
            img.className = 'w-64 h-64 border rounded-lg shadow-sm';

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('regenerateBtn').style.display = 'inline-block';
                console.log('QR Code generated successfully for auto domain');
            };

            img.onerror = function() {
                console.log('Google Charts failed, trying alternative...');
                // Method 2: Using QR Server API
                const img2 = document.createElement('img');
                img2.src = `https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=${encodedUrl}`;
                img2.alt = 'QR Code';
                img2.className = 'w-64 h-64 border rounded-lg shadow-sm';

                img2.onload = function() {
                    qrContainer.innerHTML = '';
                    qrContainer.appendChild(img2);
                    document.getElementById('downloadBtn').style.display = 'inline-block';
                    document.getElementById('regenerateBtn').style.display = 'inline-block';
                    console.log('QR Code generated with alternative service');
                };

                img2.onerror = function() {
                    console.log('All QR services failed');
                    qrContainer.innerHTML = `
                        <div class="text-center p-8">
                            <i class="fas fa-qrcode text-6xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">لا يمكن توليد رمز QR تلقائياً</p>
                            <p class="text-sm text-gray-500 mb-4">يمكنك نسخ الدومين أعلاه واستخدامه في أي موقع لتوليد رمز QR</p>
                            <a href="https://www.qr-code-generator.com/" target="_blank"
                               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg inline-block">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                إنشاء رمز QR يدوياً
                            </a>
                        </div>
                    `;
                };
            };
        }

        function regenerateQR() {
            if (currentDomain) {
                qrContainer.innerHTML = `
                    <div class="text-gray-500 p-8">
                        <i class="fas fa-spinner fa-spin text-3xl mb-3"></i>
                        <p>جاري إعادة توليد رمز QR...</p>
                    </div>
                `;
                document.getElementById('downloadBtn').style.display = 'none';
                document.getElementById('regenerateBtn').style.display = 'none';

                setTimeout(() => {
                    generateQRCode(currentDomain);
                }, 1000);
            }
        }

        function downloadQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'auto-domain-qr-code.png';
                link.href = img.src;
                link.click();
            } else {
                alert('لا يوجد رمز QR للتحميل');
            }
        }

        function copyDomain() {
            const domainInput = document.getElementById('generatedDomain');

            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(domainInput.value).then(() => {
                    showCopyFeedback();
                }).catch(() => {
                    fallbackCopy();
                });
            } else {
                fallbackCopy();
            }

            function fallbackCopy() {
                domainInput.select();
                domainInput.setSelectionRange(0, 99999);
                document.execCommand('copy');
                showCopyFeedback();
            }

            function showCopyFeedback() {
                const button = event.target.closest('button');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-2"></i>تم النسخ!';
                button.classList.remove('bg-blue-500');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-blue-500');
                }, 3000);
            }
        }

        function openDomain() {
            if (currentDomain) {
                window.open(currentDomain, '_blank');
            }
        }

        // Share functions
        function shareWhatsApp() {
            const url = encodeURIComponent(currentDomain);
            const text = encodeURIComponent('شاهد موقعي الجديد الذي تم إنشاؤه تلقائياً: ');
            window.open(`https://wa.me/?text=${text}${url}`, '_blank');
        }

        function shareTwitter() {
            const url = encodeURIComponent(currentDomain);
            const text = encodeURIComponent('شاهد موقعي الجديد الذي تم إنشاؤه تلقائياً: ');
            window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
        }

        function shareLinkedIn() {
            const url = encodeURIComponent(currentDomain);
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
        }

        function shareFacebook() {
            const url = encodeURIComponent(currentDomain);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }

        function createAnother() {
            // Reset everything
            currentDomain = '';
            currentFile = null;

            // Clear timer
            if (deploymentTimer) {
                clearInterval(deploymentTimer);
                deploymentTimer = null;
            }

            // Hide sections
            fileInfo.classList.add('hidden');
            deploymentSection.classList.add('hidden');
            resultSection.classList.remove('show');

            // Reset file input
            fileInput.value = '';

            // Reset steps
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
            steps.forEach(stepId => {
                const step = document.getElementById(stepId);
                step.classList.remove('active', 'completed');
            });

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });

            // Show success message
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = '<i class="fas fa-check mr-2"></i>جاهز لإنشاء دومين جديد!';
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Add some visual enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add floating animation to hero icons
            const heroIcons = document.querySelectorAll('.hero-section i');
            heroIcons.forEach((icon, index) => {
                icon.style.animationDelay = `${index * 0.2}s`;
            });

            // Add hover effects to feature cards
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + U to upload file
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                fileInput.click();
            }

            // Escape to create another
            if (e.key === 'Escape' && resultSection.classList.contains('show')) {
                createAnother();
            }
        });

        // Add progress bar animation
        function animateProgressBar() {
            const progressBars = document.querySelectorAll('.progress-step');
            progressBars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.opacity = '0';
                    bar.style.transform = 'translateX(-20px)';

                    setTimeout(() => {
                        bar.style.transition = 'all 0.5s ease';
                        bar.style.opacity = '1';
                        bar.style.transform = 'translateX(0)';
                    }, 100);
                }, index * 200);
            });
        }

        // Call animation when deployment starts
        const originalStartDeployment = startAutomaticDeployment;
        startAutomaticDeployment = function(file) {
            originalStartDeployment(file);
            setTimeout(animateProgressBar, 500);
        };
    </script>
</body>
</html>
