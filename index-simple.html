<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع ملفات HTML - مشاركة سهلة</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .result-section {
            display: none;
        }
        .result-section.show {
            display: block;
        }
        .qr-container {
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-upload text-blue-500 mr-3"></i>
                    رفع ملفات HTML
                </h1>
                <p class="text-gray-600">ارفع ملف HTML واحصل على رابط مباشر + رمز QR للمشاركة</p>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
                <div class="drag-area p-8 rounded-lg text-center" id="dragArea">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">اسحب ملف HTML هنا</h3>
                    <p class="text-gray-500 mb-4">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-folder-open mr-2"></i>
                        اختر ملف HTML
                    </button>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3"></i>
                            <span id="fileName" class="font-medium text-gray-700"></span>
                        </div>
                        <span id="fileSize" class="text-sm text-gray-500"></span>
                    </div>
                </div>
            </div>

            <!-- Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-lg shadow-md p-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    تم رفع الملف بنجاح!
                </h3>

                <!-- Direct Link -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الرابط المباشر:</label>
                    <div class="flex">
                        <input type="text" id="directLink" readonly 
                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg bg-gray-50 text-sm">
                        <button onclick="copyLink()" 
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-l-lg">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="openLink()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-none border-r border-white">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="mb-6 text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-4">رمز QR:</label>
                    <div class="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg qr-container" id="qrContainer">
                        <div class="text-gray-500">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>جاري توليد رمز QR...</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-download mr-2"></i>
                            تحميل رمز QR
                        </button>
                        <button onclick="regenerateQR()" id="regenerateBtn" style="display:none;"
                                class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg mr-2">
                            <i class="fas fa-redo mr-2"></i>
                            إعادة توليد
                        </button>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="text-center">
                    <h4 class="text-lg font-medium text-gray-700 mb-4">مشاركة الرابط:</h4>
                    <div class="flex justify-center space-x-4 space-x-reverse flex-wrap gap-2">
                        <button onclick="shareWhatsApp()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-whatsapp mr-2"></i>
                            واتساب
                        </button>
                        <button onclick="shareTwitter()" 
                                class="bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-twitter mr-2"></i>
                            تويتر
                        </button>
                        <button onclick="shareLinkedIn()" 
                                class="bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-linkedin mr-2"></i>
                            لينكدإن
                        </button>
                        <button onclick="shareFacebook()" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fab fa-facebook mr-2"></i>
                            فيسبوك
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 رفع ملفات HTML. جميع الحقوق محفوظة.</p>
                <div class="mt-4 space-x-4 space-x-reverse">
                    <a href="#" class="hover:text-blue-500">سياسة الخصوصية</a>
                    <a href="#" class="hover:text-blue-500">تواصل معنا</a>
                    <a href="#" class="hover:text-blue-500">GitHub</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let uploadedFiles = JSON.parse(localStorage.getItem('uploadedFiles')) || {};
        let currentFileId = null;
        let currentFileUrl = null;

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const resultSection = document.getElementById('resultSection');
        const directLink = document.getElementById('directLink');
        const qrContainer = document.getElementById('qrContainer');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            checkViewFromURL();
        });

        function setupEventListeners() {
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);
            
            // File input change
            fileInput.addEventListener('change', handleFileSelect);
            
            // Click on drag area
            dragArea.addEventListener('click', () => fileInput.click());
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                alert('يرجى اختيار ملف HTML فقط (.html أو .htm)');
                return;
            }
            
            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
                return;
            }
            
            // Show file info
            showFileInfo(file);
            
            // Process file
            processFile(file);
        }

        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function processFile(file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const content = e.target.result;
                    const fileId = generateFileId();
                    
                    // Store file in localStorage
                    uploadedFiles[fileId] = {
                        name: file.name,
                        content: content,
                        uploadDate: new Date().toISOString(),
                        size: file.size
                    };
                    
                    localStorage.setItem('uploadedFiles', JSON.stringify(uploadedFiles));
                    
                    // Generate link and show result
                    currentFileId = fileId;
                    showResult(fileId);
                    
                } catch (error) {
                    console.error('Error processing file:', error);
                    alert('حدث خطأ في معالجة الملف. يرجى المحاولة مرة أخرى.');
                }
            };
            
            reader.onerror = function() {
                alert('حدث خطأ في قراءة الملف. يرجى المحاولة مرة أخرى.');
            };
            
            reader.readAsText(file, 'UTF-8');
        }

        function generateFileId() {
            return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        function showResult(fileId) {
            try {
                const baseUrl = window.location.origin + window.location.pathname;
                const fileUrl = baseUrl + '?view=' + fileId;
                currentFileUrl = fileUrl;
                
                // Set the direct link
                directLink.value = fileUrl;
                
                // Show result section
                resultSection.classList.add('show');
                
                // Generate QR code
                setTimeout(() => {
                    generateQRCode(fileUrl);
                }, 100);
                
                // Scroll to result
                setTimeout(() => {
                    resultSection.scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'start'
                    });
                }, 200);
                
                console.log('File uploaded successfully:', fileId);
                console.log('Generated URL:', fileUrl);
                
            } catch (error) {
                console.error('Error showing result:', error);
                alert('حدث خطأ في عرض النتائج. يرجى المحاولة مرة أخرى.');
            }
        }

        function generateQRCode(url) {
            console.log('Generating QR for:', url);

            // Method 1: Using Google Charts API (most reliable)
            const img = document.createElement('img');
            const encodedUrl = encodeURIComponent(url);
            img.src = `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodedUrl}`;
            img.alt = 'QR Code';
            img.style.width = '200px';
            img.style.height = '200px';
            img.className = 'border rounded';

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('regenerateBtn').style.display = 'inline-block';
                console.log('QR Code generated successfully');
            };

            img.onerror = function() {
                console.log('Google Charts failed, trying alternative...');
                // Method 2: Using QR Server API
                const img2 = document.createElement('img');
                img2.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedUrl}`;
                img2.alt = 'QR Code';
                img2.style.width = '200px';
                img2.style.height = '200px';
                img2.className = 'border rounded';

                img2.onload = function() {
                    qrContainer.innerHTML = '';
                    qrContainer.appendChild(img2);
                    document.getElementById('downloadBtn').style.display = 'inline-block';
                    document.getElementById('regenerateBtn').style.display = 'inline-block';
                    console.log('QR Code generated with alternative service');
                };

                img2.onerror = function() {
                    console.log('All QR services failed, showing manual option');
                    qrContainer.innerHTML = `
                        <div class="text-center p-4">
                            <i class="fas fa-qrcode text-6xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">لا يمكن توليد رمز QR تلقائياً</p>
                            <p class="text-sm text-gray-500 mb-4">يمكنك نسخ الرابط أعلاه واستخدامه في أي موقع لتوليد رمز QR</p>
                            <a href="https://www.qr-code-generator.com/" target="_blank"
                               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg inline-block">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                إنشاء رمز QR يدوياً
                            </a>
                        </div>
                    `;
                };
            };
        }

        function regenerateQR() {
            if (currentFileUrl) {
                qrContainer.innerHTML = `
                    <div class="text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>جاري إعادة توليد رمز QR...</p>
                    </div>
                `;
                document.getElementById('downloadBtn').style.display = 'none';
                document.getElementById('regenerateBtn').style.display = 'none';

                setTimeout(() => {
                    generateQRCode(currentFileUrl);
                }, 500);
            }
        }

        function downloadQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'qr-code.png';
                link.href = img.src;
                link.target = '_blank';
                link.click();
            } else {
                alert('لا يوجد رمز QR للتحميل');
            }
        }

        function copyLink() {
            const linkInput = document.getElementById('directLink');

            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(linkInput.value).then(() => {
                    showCopyFeedback();
                }).catch(() => {
                    fallbackCopy();
                });
            } else {
                fallbackCopy();
            }

            function fallbackCopy() {
                linkInput.select();
                linkInput.setSelectionRange(0, 99999);
                document.execCommand('copy');
                showCopyFeedback();
            }

            function showCopyFeedback() {
                const button = event.target.closest('button');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.remove('bg-blue-500');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-blue-500');
                }, 2000);
            }
        }

        function openLink() {
            window.open(directLink.value, '_blank');
        }

        // Share functions
        function shareWhatsApp() {
            const url = encodeURIComponent(directLink.value);
            const text = encodeURIComponent('شاهد هذا الملف: ');
            window.open(`https://wa.me/?text=${text}${url}`, '_blank');
        }

        function shareTwitter() {
            const url = encodeURIComponent(directLink.value);
            const text = encodeURIComponent('شاهد هذا الملف: ');
            window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
        }

        function shareLinkedIn() {
            const url = encodeURIComponent(directLink.value);
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
        }

        function shareFacebook() {
            const url = encodeURIComponent(directLink.value);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }

        // Check if viewing a file from URL
        function checkViewFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const viewFileId = urlParams.get('view');

            if (viewFileId) {
                const file = uploadedFiles[viewFileId];
                if (file) {
                    // Show the file content
                    document.body.innerHTML = `
                        <div class="bg-gray-50 min-h-screen">
                            <header class="bg-white shadow-sm border-b p-4">
                                <div class="container mx-auto">
                                    <div class="flex items-center justify-between">
                                        <h1 class="text-xl font-bold text-gray-800">
                                            <i class="fas fa-file-code text-blue-500 mr-2"></i>
                                            ${file.name}
                                        </h1>
                                        <div class="space-x-2 space-x-reverse">
                                            <button onclick="window.history.back()"
                                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                                                <i class="fas fa-arrow-right mr-2"></i>
                                                رجوع
                                            </button>
                                            <button onclick="downloadFile()"
                                                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                                <i class="fas fa-download mr-2"></i>
                                                تحميل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </header>
                            <main class="container mx-auto p-4">
                                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                                    <iframe srcdoc="${file.content.replace(/"/g, '&quot;')}"
                                            class="w-full h-screen border-0"></iframe>
                                </div>
                            </main>
                        </div>
                        <script>
                            function downloadFile() {
                                const blob = new Blob([\`${file.content.replace(/`/g, '\\`')}\`], { type: 'text/html' });
                                const url = URL.createObjectURL(blob);
                                const link = document.createElement('a');
                                link.href = url;
                                link.download = '${file.name}';
                                link.click();
                                URL.revokeObjectURL(url);
                            }
                        </script>
                    `;
                    document.title = `عرض الملف: ${file.name}`;
                } else {
                    alert('الملف غير موجود أو تم حذفه');
                    window.location.href = window.location.pathname;
                }
            }
        }
    </script>
</body>
</html>
