<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملف تجريبي - اختبار الموقع</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        p {
            color: #666;
            line-height: 1.6;
            font-size: 1.1em;
            margin-bottom: 30px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }
        .feature p {
            color: #666;
            font-size: 0.9em;
            margin: 0;
        }
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="emoji">🚀</div>
        <h1>مرحباً بك!</h1>
        <p>هذا ملف HTML تجريبي يوضح كيفية عمل موقع رفع الملفات. يمكنك رفع أي ملف HTML والحصول على رابط مباشر مع رمز QR للمشاركة.</p>
        
        <div class="features">
            <div class="feature">
                <h3>📤 رفع سهل</h3>
                <p>اسحب وأفلت ملف HTML أو اضغط لاختيار الملف</p>
            </div>
            <div class="feature">
                <h3>🔗 رابط مباشر</h3>
                <p>احصل على رابط مباشر لمشاركة ملفك مع الآخرين</p>
            </div>
            <div class="feature">
                <h3>📱 رمز QR</h3>
                <p>رمز QR قابل للتحميل لمشاركة سهلة عبر الهاتف</p>
            </div>
            <div class="feature">
                <h3>👀 معاينة مباشرة</h3>
                <p>عرض الملف مباشرة في المتصفح أو عرض الكود المصدري</p>
            </div>
        </div>
        
        <button class="button" onclick="showAlert()">اضغط هنا للتجربة!</button>
        
        <script>
            function showAlert() {
                alert('🎉 تم تحميل الملف بنجاح! هذا مثال على ملف HTML يعمل بشكل كامل.');
            }
            
            // Add some interactive effects
            document.addEventListener('DOMContentLoaded', function() {
                const features = document.querySelectorAll('.feature');
                features.forEach((feature, index) => {
                    feature.style.animationDelay = `${index * 0.1}s`;
                    feature.style.animation = 'fadeInUp 0.6s ease forwards';
                });
            });
            
            // Add CSS animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .feature {
                    opacity: 0;
                }
            `;
            document.head.appendChild(style);
        </script>
    </div>
</body>
</html>
