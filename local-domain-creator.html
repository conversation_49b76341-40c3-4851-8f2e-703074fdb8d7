<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منشئ الدومين المحلي - يعمل بدون إنترنت</title>
    <meta name="description" content="أنشئ دومين محلي يعمل فوراً لملفات HTML">
    
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .drag-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .drag-area.dragover {
            border-color: #4299e1;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            transform: scale(1.02);
        }
        .result-section {
            display: none;
            animation: slideInUp 0.6s ease-out;
        }
        .result-section.show {
            display: block;
        }
        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .domain-preview {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .local-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: bold;
        }
        .working-indicator {
            animation: pulse 2s infinite;
        }
        .feature-highlight {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="hero-gradient text-white">
        <div class="container mx-auto px-4 py-12">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">
                    <i class="fas fa-home mr-3"></i>
                    منشئ الدومين المحلي
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    أنشئ "دومين" يعمل فوراً على جهازك بدون إنترنت
                </p>
                <div class="flex justify-center items-center space-x-8 space-x-reverse text-lg mb-8">
                    <span><i class="fas fa-bolt text-yellow-300 mr-2"></i>فوري</span>
                    <span><i class="fas fa-wifi-slash text-green-300 mr-2"></i>بدون نت</span>
                    <span><i class="fas fa-desktop text-blue-300 mr-2"></i>محلي</span>
                    <span><i class="fas fa-qrcode text-purple-300 mr-2"></i>QR</span>
                </div>
                
                <div class="bg-white bg-opacity-10 rounded-lg p-6 max-w-3xl mx-auto">
                    <h3 class="text-xl font-bold mb-4">💡 كيف يعمل؟</h3>
                    <p class="text-lg opacity-90 mb-4">
                        بدلاً من دومين حقيقي معقد، نعمل لك "دومين محلي" يعمل على جهازك فوراً
                    </p>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-upload text-xl mb-2"></i>
                            <p>ارفع ملف HTML</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-magic text-xl mb-2"></i>
                            <p>نولد رابط محلي</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded p-3">
                            <i class="fas fa-share text-xl mb-2"></i>
                            <p>شارك مع QR</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-12">
        <!-- Upload Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-xl shadow-xl p-8 mb-8">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">
                        <i class="fas fa-upload text-blue-500 mr-2"></i>
                        ارفع ملف HTML
                    </h2>
                    <p class="text-gray-600">سيتم إنشاء "دومين محلي" فوراً</p>
                    <div class="local-badge mt-3">
                        <i class="fas fa-home mr-2"></i>
                        يعمل على جهازك بدون إنترنت
                    </div>
                </div>
                
                <div class="drag-area p-12 rounded-xl text-center" id="dragArea">
                    <div class="mb-6">
                        <i class="fas fa-cloud-upload-alt text-8xl text-gray-400 mb-4 working-indicator"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-3">اسحب ملف HTML هنا</h3>
                    <p class="text-gray-500 mb-6">أو اضغط لاختيار ملف</p>
                    <input type="file" id="fileInput" accept=".html,.htm" class="hidden">
                    <button onclick="document.getElementById('fileInput').click()" 
                            class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105">
                        <i class="fas fa-folder-open mr-3"></i>
                        اختر ملف HTML
                    </button>
                    <div class="mt-6 text-sm text-gray-400">
                        <p>أي حجم • جميع ملفات HTML • يعمل فوراً</p>
                    </div>
                </div>
                
                <!-- File Info -->
                <div id="fileInfo" class="mt-6 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-code text-blue-500 mr-3 text-xl"></i>
                            <div>
                                <span id="fileName" class="font-semibold text-gray-700 block"></span>
                                <span id="fileSize" class="text-sm text-gray-500"></span>
                            </div>
                        </div>
                        <div class="text-green-600">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Section -->
            <div id="processingSection" class="bg-white rounded-xl shadow-xl p-8 mb-8 hidden">
                <div class="text-center">
                    <i class="fas fa-cog fa-spin text-4xl text-blue-500 mb-4"></i>
                    <h3 class="text-2xl font-semibold text-gray-800 mb-2">جاري إنشاء الدومين المحلي...</h3>
                    <p class="text-gray-600 mb-6">يرجى الانتظار، العملية تستغرق ثوانٍ قليلة</p>
                    
                    <div class="bg-gray-100 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm text-gray-600">التقدم:</span>
                            <span class="text-sm text-blue-600 font-semibold" id="progressText">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-blue-600 h-3 rounded-full transition-all duration-500" id="progressBar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-sm text-gray-500" id="statusText">
                        جاري معالجة الملف...
                    </div>
                </div>
            </div>

            <!-- Result Section -->
            <div id="resultSection" class="result-section bg-white rounded-xl shadow-xl p-8">
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-check text-3xl text-green-600"></i>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-800 mb-4">
                        🎉 تم إنشاء الدومين المحلي!
                    </h3>
                    <p class="text-gray-600 text-lg">دومينك المحلي جاهز ويعمل الآن</p>
                </div>

                <!-- Generated Local Domain -->
                <div class="domain-preview mb-8">
                    <div class="text-center">
                        <h4 class="text-xl font-bold mb-3">
                            <i class="fas fa-home mr-2"></i>
                            الدومين المحلي الجديد
                        </h4>
                        <div class="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
                            <input type="text" id="localDomain" readonly 
                                   class="w-full bg-transparent text-center text-xl font-mono font-bold text-white border-0 outline-0">
                        </div>
                        <div class="flex justify-center space-x-3 space-x-reverse">
                            <button onclick="copyLocalDomain()" 
                                    class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                                <i class="fas fa-copy mr-2"></i>نسخ
                            </button>
                            <button onclick="openLocalDomain()" 
                                    class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                                <i class="fas fa-external-link-alt mr-2"></i>فتح
                            </button>
                            <button onclick="shareLocalDomain()" 
                                    class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                                <i class="fas fa-share mr-2"></i>مشاركة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                    <div class="feature-highlight">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-bolt text-xl mr-3"></i>
                            <h5 class="font-bold">يعمل فوراً</h5>
                        </div>
                        <p class="text-sm opacity-90">لا يحتاج انتظار أو تفعيل</p>
                    </div>
                    
                    <div class="feature-highlight">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-wifi-slash text-xl mr-3"></i>
                            <h5 class="font-bold">بدون إنترنت</h5>
                        </div>
                        <p class="text-sm opacity-90">يعمل حتى لو انقطع النت</p>
                    </div>
                    
                    <div class="feature-highlight">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-shield-alt text-xl mr-3"></i>
                            <h5 class="font-bold">آمن تماماً</h5>
                        </div>
                        <p class="text-sm opacity-90">كل شيء على جهازك</p>
                    </div>
                    
                    <div class="feature-highlight">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-mobile-alt text-xl mr-3"></i>
                            <h5 class="font-bold">يعمل على الموبايل</h5>
                        </div>
                        <p class="text-sm opacity-90">QR للوصول من الهاتف</p>
                    </div>
                </div>

                <!-- QR Code Section -->
                <div class="text-center mb-8">
                    <h4 class="text-lg font-semibold text-gray-700 mb-4">رمز QR للدومين المحلي:</h4>
                    <div class="inline-block p-6 bg-gray-50 border-2 border-gray-200 rounded-xl shadow-lg" id="qrContainer">
                        <div class="text-gray-500">
                            <i class="fas fa-spinner fa-spin text-3xl mb-3"></i>
                            <p>جاري توليد رمز QR...</p>
                        </div>
                    </div>
                    <div class="mt-6">
                        <button onclick="downloadQR()" id="downloadBtn" style="display:none;"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg mr-3 transition-all">
                            <i class="fas fa-download mr-2"></i>
                            تحميل QR
                        </button>
                        <button onclick="printQR()" id="printBtn" style="display:none;"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-all">
                            <i class="fas fa-print mr-2"></i>
                            طباعة QR
                        </button>
                    </div>
                </div>

                <!-- Create Another -->
                <div class="text-center">
                    <button onclick="createAnother()" 
                            class="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105">
                        <i class="fas fa-plus mr-3"></i>
                        إنشاء دومين آخر
                    </button>
                </div>
            </div>
        </div>

        <!-- How It Works -->
        <div class="max-w-4xl mx-auto mt-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">كيف يعمل الدومين المحلي؟</h2>
                <p class="text-gray-600 text-lg">فهم بسيط لما يحدث خلف الكواليس</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-upload text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">1. رفع الملف</h3>
                    <p class="text-gray-600">نقرأ ملف HTML ونحفظه في ذاكرة المتصفح</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-link text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">2. إنشاء رابط</h3>
                    <p class="text-gray-600">نولد رابط محلي فريد يشير للملف المحفوظ</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-qrcode text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">3. QR للمشاركة</h3>
                    <p class="text-gray-600">نولد QR يمكن مسحه من أي جهاز للوصول للملف</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12 mt-16">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h3 class="text-2xl font-bold mb-4">
                    <i class="fas fa-home mr-2"></i>
                    منشئ الدومين المحلي
                </h3>
                <p class="text-gray-400 mb-6">الحل العملي لإنشاء "دومين" يعمل فوراً على جهازك</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                    <div>
                        <h4 class="font-bold mb-2">المميزات</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>✅ يعمل بدون إنترنت</li>
                            <li>✅ فوري ومباشر</li>
                            <li>✅ آمن 100%</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">الاستخدامات</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>📱 عرض على الموبايل</li>
                            <li>🖥️ مشاركة محلية</li>
                            <li>🎯 اختبار سريع</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">التقنية</h4>
                        <ul class="text-gray-400 text-sm space-y-1">
                            <li>🔧 Blob URLs</li>
                            <li>💾 Local Storage</li>
                            <li>📱 QR Generation</li>
                        </ul>
                    </div>
                </div>
                <div class="mt-8 pt-8 border-t border-gray-700 text-gray-400">
                    <p>&copy; 2024 منشئ الدومين المحلي. حل عملي للمطورين.</p>
                    <p class="mt-2">
                        <i class="fas fa-lightbulb text-yellow-500"></i>
                        فكرة ذكية لتوفير الوقت والجهد
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let currentFile = null;
        let currentBlobUrl = null;
        let localDomains = JSON.parse(localStorage.getItem('localDomains')) || [];

        // DOM Elements
        const dragArea = document.getElementById('dragArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const processingSection = document.getElementById('processingSection');
        const resultSection = document.getElementById('resultSection');
        const localDomain = document.getElementById('localDomain');
        const qrContainer = document.getElementById('qrContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const statusText = document.getElementById('statusText');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Local Domain Creator loaded');
            setupEventListeners();
        });

        function setupEventListeners() {
            // Drag and drop events
            dragArea.addEventListener('dragover', handleDragOver);
            dragArea.addEventListener('dragleave', handleDragLeave);
            dragArea.addEventListener('drop', handleDrop);

            // File input change
            fileInput.addEventListener('change', handleFileSelect);

            // Click on drag area
            dragArea.addEventListener('click', () => fileInput.click());
        }

        function handleDragOver(e) {
            e.preventDefault();
            dragArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dragArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
                showNotification('يرجى اختيار ملف HTML فقط (.html أو .htm)', 'error');
                return;
            }

            currentFile = file;
            showFileInfo(file);

            // Start processing
            setTimeout(() => startProcessing(), 500);
        }

        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function startProcessing() {
            processingSection.classList.remove('hidden');

            const steps = [
                { progress: 20, status: 'جاري قراءة الملف...' },
                { progress: 40, status: 'جاري إنشاء الرابط المحلي...' },
                { progress: 60, status: 'جاري حفظ البيانات...' },
                { progress: 80, status: 'جاري توليد المعرف الفريد...' },
                { progress: 100, status: 'تم الانتهاء!' }
            ];

            let currentStep = 0;

            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    updateProgress(step.progress, step.status);
                    currentStep++;
                } else {
                    clearInterval(interval);
                    setTimeout(() => {
                        processFile();
                    }, 1000);
                }
            }, 800);
        }

        function updateProgress(progress, status) {
            progressBar.style.width = progress + '%';
            progressText.textContent = progress + '%';
            statusText.textContent = status;
        }

        function processFile() {
            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    const content = e.target.result;

                    // Create blob URL (this is our "local domain")
                    const blob = new Blob([content], { type: 'text/html' });
                    currentBlobUrl = URL.createObjectURL(blob);

                    // Generate a nice looking "domain" name
                    const timestamp = Date.now().toString(36);
                    const random = Math.random().toString(36).substr(2, 5);
                    const baseName = currentFile.name.replace(/\.[^/.]+$/, "").toLowerCase().replace(/[^a-z0-9]/g, '-');

                    const localDomainName = `local://${baseName}-${timestamp}-${random}.html`;

                    // Save to local storage for persistence
                    const domainData = {
                        id: Date.now(),
                        name: currentFile.name,
                        localDomain: localDomainName,
                        blobUrl: currentBlobUrl,
                        content: content,
                        createdAt: new Date().toISOString(),
                        size: currentFile.size
                    };

                    localDomains.unshift(domainData);
                    localStorage.setItem('localDomains', JSON.stringify(localDomains));

                    // Show results
                    showResults(localDomainName, currentBlobUrl);

                } catch (error) {
                    console.error('Error processing file:', error);
                    showNotification('حدث خطأ في معالجة الملف', 'error');
                }
            };

            reader.onerror = function() {
                showNotification('فشل في قراءة الملف', 'error');
            };

            reader.readAsText(currentFile, 'UTF-8');
        }

        function showResults(domainName, blobUrl) {
            // Hide processing, show results
            processingSection.classList.add('hidden');
            localDomain.value = domainName;
            resultSection.classList.add('show');

            // Generate QR code
            setTimeout(() => {
                generateQRCode(blobUrl);
            }, 500);

            // Scroll to results
            setTimeout(() => {
                resultSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 1000);

            showNotification('تم إنشاء الدومين المحلي بنجاح!', 'success');
        }

        function generateQRCode(url) {
            console.log('Generating QR for local domain:', url);

            // For QR, we'll use the actual blob URL since it works
            const img = document.createElement('img');
            img.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;
            img.alt = 'QR Code';
            img.className = 'w-48 h-48 border rounded-lg shadow-sm';

            img.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(img);
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('printBtn').style.display = 'inline-block';
                console.log('QR Code generated successfully');
            };

            img.onerror = function() {
                console.log('QR generation failed, showing manual option');
                qrContainer.innerHTML = `
                    <div class="text-center p-6">
                        <i class="fas fa-qrcode text-6xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-4">QR غير متاح حالياً</p>
                        <p class="text-sm text-gray-500">استخدم زر "فتح" للوصول للملف</p>
                    </div>
                `;
            };
        }

        // Action functions
        function copyLocalDomain() {
            const domainText = localDomain.value;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(domainText).then(() => {
                    showCopySuccess();
                });
            } else {
                localDomain.select();
                document.execCommand('copy');
                showCopySuccess();
            }

            function showCopySuccess() {
                showNotification('تم نسخ الدومين المحلي', 'success');
            }
        }

        function openLocalDomain() {
            if (currentBlobUrl) {
                window.open(currentBlobUrl, '_blank');
                showNotification('تم فتح الدومين المحلي', 'success');
            } else {
                showNotification('لا يوجد دومين للفتح', 'error');
            }
        }

        function shareLocalDomain() {
            if (navigator.share && currentBlobUrl) {
                navigator.share({
                    title: 'دومين محلي - ' + currentFile.name,
                    text: 'شاهد هذا الملف:',
                    url: currentBlobUrl
                }).then(() => {
                    showNotification('تم المشاركة بنجاح', 'success');
                }).catch(() => {
                    fallbackShare();
                });
            } else {
                fallbackShare();
            }

            function fallbackShare() {
                // Copy to clipboard as fallback
                copyLocalDomain();
                showNotification('تم نسخ الرابط للمشاركة', 'info');
            }
        }

        function downloadQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const link = document.createElement('a');
                link.download = 'local-domain-qr.png';
                link.href = img.src;
                link.click();
                showNotification('تم تحميل رمز QR', 'success');
            }
        }

        function printQR() {
            const img = qrContainer.querySelector('img');
            if (img) {
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>طباعة رمز QR</title>
                            <style>
                                body {
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    min-height: 100vh;
                                    margin: 0;
                                    font-family: Arial, sans-serif;
                                }
                                .container { text-align: center; }
                                img { max-width: 300px; margin: 20px; }
                                h2 { color: #333; }
                                p { color: #666; font-size: 14px; }
                            </style>
                        </head>
                        <body>
                            <div class="container">
                                <h2>رمز QR للدومين المحلي</h2>
                                <img src="${img.src}" alt="QR Code">
                                <p>اسم الملف: ${currentFile.name}</p>
                                <p>تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}</p>
                            </div>
                        </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.print();
                showNotification('تم إرسال للطباعة', 'success');
            }
        }

        function createAnother() {
            // Reset everything
            currentFile = null;
            if (currentBlobUrl) {
                URL.revokeObjectURL(currentBlobUrl);
                currentBlobUrl = null;
            }

            // Hide sections
            fileInfo.classList.add('hidden');
            processingSection.classList.add('hidden');
            resultSection.classList.remove('show');

            // Reset file input
            fileInput.value = '';

            // Reset progress
            progressBar.style.width = '0%';
            progressText.textContent = '0%';
            statusText.textContent = 'جاري معالجة الملف...';

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });

            showNotification('جاهز لإنشاء دومين جديد!', 'info');
        }

        // Utility functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' :
                           type === 'error' ? 'bg-red-500' :
                           type === 'info' ? 'bg-blue-500' : 'bg-gray-500';

            const icon = type === 'success' ? 'fa-check' :
                        type === 'error' ? 'fa-exclamation-triangle' :
                        type === 'info' ? 'fa-info' : 'fa-bell';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 4 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Cleanup blob URLs when page unloads
        window.addEventListener('beforeunload', function() {
            if (currentBlobUrl) {
                URL.revokeObjectURL(currentBlobUrl);
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + O to open file
            if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
                e.preventDefault();
                fileInput.click();
            }

            // Ctrl/Cmd + N for new domain
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                if (resultSection.classList.contains('show')) {
                    createAnother();
                }
            }

            // Space to open current domain
            if (e.key === ' ' && currentBlobUrl && resultSection.classList.contains('show')) {
                e.preventDefault();
                openLocalDomain();
            }
        });

        // Show welcome message
        setTimeout(() => {
            showNotification('مرحباً! ارفع ملف HTML لإنشاء دومين محلي', 'info');
        }, 1000);

        console.log('Local Domain Creator loaded successfully');
    </script>
</body>
</html>
